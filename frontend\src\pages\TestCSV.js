import React, { useState, useEffect } from 'react';

const TestCSV = () => {
  const [csvData, setCsvData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadCSV = async () => {
      try {
        console.log('🔍 Testing CSV loading...');
        const response = await fetch('/uploads/plant-features.csv');
        console.log('📡 Response status:', response.status);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const csvText = await response.text();
        console.log('📄 CSV text length:', csvText.length);
        console.log('📄 First 200 characters:', csvText.substring(0, 200));
        
        const lines = csvText.split('\n');
        console.log('📊 Total lines:', lines.length);
        
        const plants = [];
        for (let i = 1; i < Math.min(lines.length, 6); i++) { // Only process first 5 plants for testing
          if (lines[i].trim()) {
            const values = parseCSVLine(lines[i]);
            console.log(`🌿 Line ${i}:`, values);
            if (values.length >= 4) {
              plants.push({
                scientificName: values[0],
                localName: values[1],
                features: values[2],
                photo: values[3],
                index: i - 1
              });
            }
          }
        }
        
        console.log('✅ Parsed plants:', plants);
        setCsvData(plants);
        setLoading(false);
        
      } catch (error) {
        console.error('❌ Error loading CSV:', error);
        setError(error.message);
        setLoading(false);
      }
    };

    loadCSV();
  }, []);

  const parseCSVLine = (line) => {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim().replace(/^"|"$/g, ''));
        current = '';
      } else {
        current += char;
      }
    }
    result.push(current.trim().replace(/^"|"$/g, ''));
    return result;
  };

  if (loading) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h2>🔍 Testing CSV Loading...</h2>
        <p>Loading plant-features.csv...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>
        <h2>❌ Error Loading CSV</h2>
        <p>{error}</p>
        <p>Check console for more details.</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px' }}>
      <h2>✅ CSV Test Results</h2>
      <p><strong>Plants loaded:</strong> {csvData.length}</p>
      
      <div style={{ marginTop: '20px' }}>
        <h3>First 5 Plants:</h3>
        {csvData.map((plant, index) => (
          <div key={index} style={{
            border: '1px solid #ccc',
            padding: '10px',
            margin: '10px 0',
            borderRadius: '5px'
          }}>
            <h4>{plant.scientificName}</h4>
            <p><strong>Local Name:</strong> {plant.localName}</p>
            <p><strong>Features:</strong> {plant.features.substring(0, 100)}...</p>
            <p><strong>Photo:</strong> {plant.photo}</p>
          </div>
        ))}
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <button onClick={() => window.location.href = '/dual-search'}>
          Go to Dual Search
        </button>
      </div>
    </div>
  );
};

export default TestCSV;
