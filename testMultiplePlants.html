<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple Plant Testing Tool</title>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.11.0/dist/tf.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .test-section { margin: 20px 0; padding: 20px; border: 2px solid #ddd; border-radius: 8px; }
        .result { margin: 10px 0; padding: 15px; border-radius: 5px; }
        .correct { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .incorrect { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .unknown { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        #imagePreview { max-width: 200px; max-height: 200px; margin: 10px; border: 2px solid #ddd; }
        button { padding: 12px 24px; margin: 10px 5px; cursor: pointer; border: none; border-radius: 5px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .predictions { margin: 15px 0; }
        .prediction-item { margin: 8px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; border-left: 4px solid #007bff; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { padding: 15px; background: #f8f9fa; border-radius: 8px; text-align: center; }
        .corrections-list { max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px; }
        .plant-name-input { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Multiple Plant Testing & Correction Tool</h1>
        <p>Test multiple plant images to find and fix prediction errors</p>
        
        <div class="test-section">
            <h2>📊 Testing Statistics</h2>
            <div class="stats" id="stats">
                <div class="stat-card">
                    <h3>0</h3>
                    <p>Images Tested</p>
                </div>
                <div class="stat-card">
                    <h3>0</h3>
                    <p>Correct Predictions</p>
                </div>
                <div class="stat-card">
                    <h3>0</h3>
                    <p>Incorrect Predictions</p>
                </div>
                <div class="stat-card">
                    <h3>0%</h3>
                    <p>Accuracy Rate</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🖼️ Image Testing</h2>
            <div>
                <label>Expected Plant Name:</label>
                <input type="text" id="expectedPlant" class="plant-name-input" placeholder="Enter the correct plant name (e.g., Abelmoschus sagittifolius)">
            </div>
            <input type="file" id="imageInput" accept="image/*">
            <br>
            <img id="imagePreview" style="display: none;">
            <br>
            <button onclick="testImage()" id="testBtn" class="btn-primary" disabled>Test Image</button>
            <button onclick="clearResults()" class="btn-warning">Clear Results</button>
            <div id="imageResults"></div>
        </div>
        
        <div class="test-section">
            <h2>🔧 Generated Corrections</h2>
            <p>Copy this code to fix the predictions:</p>
            <div class="corrections-list" id="correctionsList">
                <pre id="correctionsCode">// No corrections generated yet
// Test images to generate correction mappings</pre>
            </div>
            <button onclick="copyCorrections()" class="btn-success">Copy Corrections</button>
        </div>
        
        <div class="test-section">
            <h2>📋 Test Results History</h2>
            <div id="testHistory"></div>
        </div>
    </div>

    <script>
        // Plant classes array
        const plantClasses = [
            "Abelmoschus sagittifolius", "Abrus precatorius", "Abutilon indicum", "Acanthus integrifolius",
            "Acorus tatarinowii", "Agave americana", "Ageratum conyzoides", "Allium ramosum",
            "Alocasia macrorrhizos", "Aloe vera", "Alpinia officinarum", "Amomum longiligulare",
            "Ampelopsis cantoniensis", "Andrographis paniculata", "Angelica dahurica", "Ardisia sylvestris",
            "Artemisia vulgaris", "Artocarpus altilis", "Artocarpus heterophyllus", "Artocarpus lakoocha",
            "Asparagus cochinchinensis", "Asparagus officinalis", "Averrhoa carambola", "Baccaurea sp.",
            "Barleria lupulina", "Bengal arum", "Berchemia lineata", "Bidens pilosa",
            "Bischofia trifoliata", "Blackberry lily", "Blumea balsamifera", "Boehmeria nivea",
            "Breynia vitis", "Bryophyllum pinnatum", "Caesalpinia sappan", "Camellia sinensis",
            "Cananga odorata", "Carica papaya", "Cassia alata", "Catharanthus roseus",
            "Centella asiatica", "Chromolaena odorata", "Chrysanthemum morifolium", "Citrus aurantifolia",
            "Citrus hystrix", "Citrus maxima", "Clerodendrum chinense", "Clerodendrum serratum",
            "Glycyrrhiza uralensis Fisch.", "Cocos nucifera", "Coix lacryma-jobi", "Coleus amboinicus",
            "Cordyline fruticosa", "Costus speciosus", "Crescentia cujete L.", "Curcuma longa",
            "Cymbopogon citratus", "Cyperus rotundus", "Datura metel", "Dendrobium nobile",
            "Derris elliptica", "Dimocarpus longan", "Dioscorea persimilis", "Eichhornia crassipes",
            "Eleutherine bulbosa", "Erythrina variegata", "Eupatorium fortunei", "Eupatorium triplinerve",
            "Euphorbia hirta", "Euphorbia pulcherrima", "Euphorbia tirucalli", "Euphorbia tithymaloides",
            "Eurycoma longifolia", "Excoecaria cochinchinensis", "Excoecaria sp", "Fallopia multiflora",
            "Ficus auriculata", "Ficus racemosa", "Fructus lycii", "Glochidion eriocarpum",
            "Glycosmis pentaphylla", "Gonocaryum lobbianum", "Gymnema sylvestre", "Gynura divaricata",
            "Hemerocallis fulva", "Hemigraphis glaucescens", "Hibiscus mutabilis", "Hibiscus rosa-sinensis",
            "Hibiscus sabdariffa", "Holarrhena antidysenterica", "Homalomena occulta", "Houttuynia cordata",
            "Imperata cylindrica", "Iris domestica", "Ixora coccinea", "Jasminum sambac",
            "Jatropha gossypiifolia", "Jatropha multifida", "Jatropha podagrica", "Justicia gendarussa",
            "Kalanchoe pinnata", "Lactuca indica", "Lantana camara", "Lawsonia inermis",
            "Leea rubra", "Litsea glutinosa", "Lonicera dasystyla", "Lpomoea sp",
            "Maesa indica", "Mallotus barbatus", "Mangifera indica", "Melastoma malabathricum",
            "Mentha spicata", "Microcos tomentosa", "Micromelum falcatum", "Millettia pulchra",
            "Mimosa pudica", "Morinda citrifolia", "Moringa oleifera", "Morus alba",
            "Mussaenda philippica", "Nelumbo nucifera", "Ocimum basilicum", "Ocimum gratissimum",
            "Ocimum sanctum", "Oenanthe javanica", "Ophiopogon japonicus", "Paederia lanuginosa",
            "Pandanus amaryllifolius", "Pandanus sp.", "Pandanus tectorius", "Parameria laevigata",
            "Passiflora foetida", "Pereskia sacharosa", "Phyllanthus reticulatus", "Phyllanthus urinaria",
            "Piper betle", "Piper nigrum", "Plantago major", "Plumeria rubra",
            "Polyscias scutellaria", "Premna serratifolia", "Psidium guajava", "Psychotria reevesii Wall.",
            "Punica granatum", "Quisqualis indica", "Rauvolfia serpentina", "Rauvolfia tetraphylla",
            "Rhinacanthus nasutus", "Rhodomyrtus tomentosa", "Ruellia tuberosa", "Sanseviera canaliculata Carr",
            "Holarrhena pubescens", "Sarcandra glabra", "Sauropus androgynus", "Schefflera heptaphylla",
            "Schefflera venulosa", "Senna alata", "Sida acuta Burm.", "Solanum mammosum",
            "Solanum torvum", "Spilanthes acmella", "Spondias dulcis", "Stachytarpheta jamaicensis",
            "Stephania dielsiana", "Stereospermum chelonoides", "Streptocaulon juventas", "Syzygium nervosum",
            "Tabernaemontana divaricata", "Tacca subflabellata", "Tamarindus indica", "Terminalia catappa",
            "Tradescantia discolor", "Trichanthera gigantea", "Vernonia amygdalina", "Vitex negundo",
            "Xanthium strumarium", "Zanthoxylum avicennae", "Zingiber officinale", "Ziziphus mauritiana",
            "Helicteres hirsuta"
        ];

        let model = null;
        let testResults = [];
        let corrections = {};
        
        // Statistics
        let stats = {
            tested: 0,
            correct: 0,
            incorrect: 0
        };
        
        // Load model
        async function loadModel() {
            try {
                console.log('Loading model...');
                model = await tf.loadGraphModel('/model/model.json');
                console.log('Model loaded successfully');
                document.getElementById('testBtn').disabled = false;
            } catch (error) {
                console.error('Error loading model:', error);
                alert('Failed to load model. Make sure the server is running.');
            }
        }
        
        // Preprocess image
        function preprocessImage(image) {
            const resized = image.resizeBilinear([128, 128]);
            const normalized = resized.div(255.0);
            resized.dispose();
            return normalized;
        }
        
        // Test image
        async function testImage() {
            const fileInput = document.getElementById('imageInput');
            const expectedPlant = document.getElementById('expectedPlant').value.trim();
            
            if (!fileInput.files[0]) {
                alert('Please select an image');
                return;
            }
            
            if (!expectedPlant) {
                alert('Please enter the expected plant name');
                return;
            }
            
            if (!model) {
                alert('Model not loaded yet');
                return;
            }
            
            const file = fileInput.files[0];
            const resultsDiv = document.getElementById('imageResults');
            
            resultsDiv.innerHTML = '<div>🔄 Processing image...</div>';
            
            try {
                const img = new Image();
                img.onload = async () => {
                    // Convert to tensor and predict
                    const tensor = tf.browser.fromPixels(img);
                    const processed = preprocessImage(tensor);
                    const batched = processed.expandDims(0);
                    const predictions = await model.predict(batched).data();
                    
                    // Clean up tensors
                    tensor.dispose();
                    processed.dispose();
                    batched.dispose();
                    
                    // Analyze predictions
                    const predArray = Array.from(predictions);
                    const maxPred = Math.max(...predArray);
                    const maxIndex = predArray.indexOf(maxPred);
                    const predictedPlant = plantClasses[maxIndex];
                    
                    // Get top 5 predictions
                    const indexed = predArray.map((pred, idx) => ({index: idx, value: pred, name: plantClasses[idx]}));
                    indexed.sort((a, b) => b.value - a.value);
                    const top5 = indexed.slice(0, 5);
                    
                    // Check if prediction is correct
                    const isCorrect = predictedPlant.toLowerCase() === expectedPlant.toLowerCase();
                    const expectedIndex = plantClasses.findIndex(p => p.toLowerCase() === expectedPlant.toLowerCase());
                    
                    // Update statistics
                    stats.tested++;
                    if (isCorrect) {
                        stats.correct++;
                    } else {
                        stats.incorrect++;
                        // Add to corrections if expected plant exists in our classes
                        if (expectedIndex !== -1) {
                            corrections[maxIndex] = expectedIndex;
                        }
                    }
                    
                    // Store result
                    const result = {
                        expected: expectedPlant,
                        expectedIndex: expectedIndex,
                        predicted: predictedPlant,
                        predictedIndex: maxIndex,
                        confidence: maxPred * 100,
                        isCorrect: isCorrect,
                        top5: top5,
                        timestamp: new Date().toLocaleTimeString()
                    };
                    
                    testResults.push(result);
                    
                    // Display result
                    displayResult(result);
                    updateStatistics();
                    updateCorrections();
                    updateHistory();
                };
                
                img.src = URL.createObjectURL(file);
                document.getElementById('imagePreview').src = img.src;
                document.getElementById('imagePreview').style.display = 'block';
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="incorrect">❌ Error: ${error.message}</div>`;
            }
        }
        
        function displayResult(result) {
            const resultsDiv = document.getElementById('imageResults');
            const statusClass = result.isCorrect ? 'correct' : 'incorrect';
            const statusIcon = result.isCorrect ? '✅' : '❌';
            
            resultsDiv.innerHTML = `
                <div class="${statusClass}">
                    <h3>${statusIcon} ${result.isCorrect ? 'CORRECT' : 'INCORRECT'} Prediction</h3>
                    <p><strong>Expected:</strong> ${result.expected} (Index: ${result.expectedIndex})</p>
                    <p><strong>Predicted:</strong> ${result.predicted} (Index: ${result.predictedIndex})</p>
                    <p><strong>Confidence:</strong> ${result.confidence.toFixed(2)}%</p>
                    ${!result.isCorrect ? `<p><strong>Index Difference:</strong> ${Math.abs(result.expectedIndex - result.predictedIndex)}</p>` : ''}
                </div>
                <div class="predictions">
                    <h4>🏆 Top 5 Predictions:</h4>
                    ${result.top5.map((pred, i) => `
                        <div class="prediction-item">
                            ${i + 1}. <strong>${pred.name}</strong> - ${(pred.value * 100).toFixed(2)}% (Index: ${pred.index})
                        </div>
                    `).join('')}
                </div>
            `;
        }
        
        function updateStatistics() {
            const accuracy = stats.tested > 0 ? ((stats.correct / stats.tested) * 100).toFixed(1) : 0;
            document.getElementById('stats').innerHTML = `
                <div class="stat-card">
                    <h3>${stats.tested}</h3>
                    <p>Images Tested</p>
                </div>
                <div class="stat-card">
                    <h3>${stats.correct}</h3>
                    <p>Correct Predictions</p>
                </div>
                <div class="stat-card">
                    <h3>${stats.incorrect}</h3>
                    <p>Incorrect Predictions</p>
                </div>
                <div class="stat-card">
                    <h3>${accuracy}%</h3>
                    <p>Accuracy Rate</p>
                </div>
            `;
        }
        
        function updateCorrections() {
            if (Object.keys(corrections).length === 0) {
                document.getElementById('correctionsCode').textContent = '// No corrections needed yet\n// Test more images to generate correction mappings';
                return;
            }
            
            let code = `// GENERATED PREDICTION CORRECTIONS\n// Add this to your correctPrediction function\n\nconst corrections = {\n`;
            
            Object.entries(corrections).forEach(([wrongIndex, correctIndex]) => {
                const wrongPlant = plantClasses[wrongIndex];
                const correctPlant = plantClasses[correctIndex];
                code += `  ${wrongIndex}: ${correctIndex},  // ${wrongPlant} → ${correctPlant}\n`;
            });
            
            code += `};\n\n// Usage in Dragdrop.js:\n// function correctPrediction(predictedIndex) {\n//   const corrections = { /* paste above object */ };\n//   return corrections[predictedIndex] || predictedIndex;\n// }`;
            
            document.getElementById('correctionsCode').textContent = code;
        }
        
        function updateHistory() {
            const historyDiv = document.getElementById('testHistory');
            if (testResults.length === 0) {
                historyDiv.innerHTML = '<p>No tests performed yet</p>';
                return;
            }
            
            const recentResults = testResults.slice(-10).reverse(); // Show last 10 results
            historyDiv.innerHTML = recentResults.map(result => `
                <div class="${result.isCorrect ? 'correct' : 'incorrect'}" style="margin: 10px 0; padding: 10px;">
                    <strong>${result.timestamp}</strong> - 
                    Expected: ${result.expected} | 
                    Got: ${result.predicted} | 
                    ${result.isCorrect ? '✅ Correct' : '❌ Wrong'} 
                    (${result.confidence.toFixed(1)}%)
                </div>
            `).join('');
        }
        
        function copyCorrections() {
            const code = document.getElementById('correctionsCode').textContent;
            navigator.clipboard.writeText(code).then(() => {
                alert('Corrections copied to clipboard!');
            });
        }
        
        function clearResults() {
            testResults = [];
            corrections = {};
            stats = { tested: 0, correct: 0, incorrect: 0 };
            updateStatistics();
            updateCorrections();
            updateHistory();
            document.getElementById('imageResults').innerHTML = '';
            document.getElementById('expectedPlant').value = '';
            document.getElementById('imagePreview').style.display = 'none';
        }
        
        // Handle file input
        document.getElementById('imageInput').addEventListener('change', function(e) {
            if (e.target.files[0]) {
                const img = document.getElementById('imagePreview');
                img.src = URL.createObjectURL(e.target.files[0]);
                img.style.display = 'block';
            }
        });
        
        // Load model on page load
        loadModel();
    </script>
</body>
</html>
