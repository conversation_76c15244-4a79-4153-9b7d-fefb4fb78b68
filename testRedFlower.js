import axios from 'axios';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current file path
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the test image
const testImagePath = path.join(process.cwd(), 'public/uploads/sample.jpg');

async function testImagePrediction() {
  try {
    // Check if the test image exists
    if (!fs.existsSync(testImagePath)) {
      console.error(`Test image not found at ${testImagePath}`);
      return;
    }

    console.log(`Using test image: ${testImagePath}`);
    console.log('This appears to be a red flower, likely a Hibiscus species');

    // Create form data for the image upload without a prediction
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));

    console.log('Sending image to server for prediction...');
    
    // Send the request to the server
    const response = await axios.post('http://localhost:8080/api/v1/plant/result', formData, {
      headers: {
        ...formData.getHeaders(),
      },
    });

    console.log('\nServer response for red flower:');
    console.log(JSON.stringify(response.data, null, 2));

    if (response.data.success) {
      console.log(`\n✅ Test completed successfully.`);
      console.log(`Predicted plant: ${response.data.name}`);
      
      // Check if the prediction is likely a Hibiscus or red flower
      const prediction = response.data.name.toLowerCase();
      if (prediction.includes('hibiscus') || 
          prediction.includes('rosa') || 
          prediction.includes('roseus') || 
          prediction.includes('red')) {
        console.log('✅ The prediction appears to match the red flower in the image.');
      } else {
        console.log('⚠️ The prediction may not match the red flower in the image.');
        console.log('The image shows what appears to be a Hibiscus or similar red flowering plant.');
      }
    } else {
      console.log('❌ Test failed');
    }
  } catch (error) {
    console.error('Error testing image prediction:', error.message);
    if (error.response) {
      console.error('Server response:', error.response.data);
    } else {
      console.error('Full error:', error);
    }
  }
}

testImagePrediction();