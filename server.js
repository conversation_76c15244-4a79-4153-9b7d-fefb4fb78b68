import express from "express";
import dotenv from "dotenv";
import morgan from "morgan";
import colors from "colors";
import connectDB from "./config/db.js";
import plantRoute from "./routes/plantRoute.js";
import cors from "cors";
import path from "path";
import { fileURLToPath } from "url";

// Config dotenv
dotenv.config();

// Debugging: Log the MONGO_URI
console.log("MongoDB URI:", process.env.MONGO_URI);

// Database connection
connectDB();

// Express app initialization
const app = express();

// Middleware setup
app.use(express.json());
app.use(morgan("dev"));

// CORS Configuration
const corsOptions = {
  origin: process.env.CLIENT_URL || "*", // Update with your frontend URL if needed
  methods: "GET,POST,PUT,DELETE",
  allowedHeaders: "Content-Type,Authorization",
};
app.use(cors(corsOptions));

// Static folder for uploads
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
app.use("/public/uploads", express.static(path.join(__dirname, "public/uploads")));

// Routes
app.use("/api/v1/plant", plantRoute);

// Root API response
app.get("/", (req, res) => {
  res.send("<h1>Welcome to Med-Plant project</h1>");
});

// Global Error Handler
app.use((err, req, res, next) => {
  console.error("Server Error:", err);
  res.status(500).json({ message: "Internal Server Error" });
});

// Server Port
const port = process.env.PORT || 8081;

// Start server
app.listen(port, () => {
  console.log(`✅ Server running on http://localhost:${port}`.bgCyan.black);
});
