import axios from "axios";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import "../style/responseModel.css";
import Footer from "./footer";

const Result = () => {
  const params = useParams();
  const navigate = useNavigate();
  const [scientificName, setscientificName] = useState("");
  const [localName, setLocalName] = useState("");
  const [description, setDescription] = useState("");
  const [photo, setPhoto] = useState("");

  const getSingleProduct = async () => {
    try {
      console.log("🔍 Fetching plant data for:", params.slug);
      const { data } = await axios.get(`/api/v1/plant/${params.slug}`);

      console.log("📊 Received plant data:", data);

      if (data.success && data.plant) {
        const plant = data.plant;

        // Set all plant information
        setscientificName(plant.scientificName || "Scientific name not available");
        setLocalName(plant.localName || "Local name not available");
        setDescription(plant.features || "Medicinal features not available");
        setPhoto(plant.photo || "https://via.placeholder.com/400x300/f8f9fa/6c757d?text=No+Image+Available");

        console.log("✅ Plant information loaded:");
        console.log("   Scientific Name:", plant.scientificName);
        console.log("   Local Name:", plant.localName);
        console.log("   Features:", plant.features);
        console.log("   Photo:", plant.photo);

      } else {
        console.warn("⚠️ Invalid plant data structure:", data);
        // Set fallback information
        setscientificName("Plant information not found");
        setLocalName("Unknown plant");
        setDescription("No medicinal information available");
        setPhoto("https://via.placeholder.com/400x300/f8f9fa/6c757d?text=Plant+Not+Found");
      }

    } catch (error) {
      console.error("❌ Error fetching plant data:", error);

      // Set error information
      setscientificName("Error loading plant data");
      setLocalName("Please try again");
      setDescription("Unable to load medicinal information");
      setPhoto("https://via.placeholder.com/400x300/f8d7da/721c24?text=Error+Loading+Image");
    }
  };
  const handleClick = () => {
    try {
      navigate("/feedback");
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getSingleProduct();
    //eslint-disable-next-line
  }, []);

  // Enhanced medicinal features processing
  const features = description ? description.split(",").map(f => f.trim()).filter(f => f.length > 0) : [];

  const arrayDataItems = features.length > 0 ? features.map((feature, index) => (
    <li key={index} style={{
      padding: '8px 0',
      borderBottom: '1px solid #eee',
      fontSize: '16px',
      color: '#2c5530'
    }}>
      🌿 {feature}
    </li>
  )) : [
    <li key="no-features" style={{
      padding: '8px 0',
      fontSize: '16px',
      color: '#666',
      fontStyle: 'italic'
    }}>
      ℹ️ Medicinal features information not available
    </li>
  ];

  return (
    <>
      <div className="response-block">
        <div style={{
          background: 'linear-gradient(135deg, #2c5530 0%, #4a7c59 100%)',
          color: 'white',
          padding: '20px',
          borderRadius: '10px 10px 0 0',
          textAlign: 'center'
        }}>
          <h1 className="response_heading" style={{
            margin: '0',
            fontSize: '2.5em',
            textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
          }}>
            🌿 {localName || scientificName}
          </h1>
          <p style={{
            margin: '10px 0 0 0',
            fontSize: '1.2em',
            opacity: '0.9'
          }}>
            Medicinal Plant Information & Recommendations
          </p>
        </div>

        <div className="response-content" style={{padding: '30px'}}>
          <div className="right_side" style={{flex: '1', marginRight: '30px'}}>

            {/* Scientific Information */}
            <div style={{
              background: '#f8f9fa',
              padding: '20px',
              borderRadius: '10px',
              marginBottom: '20px',
              border: '2px solid #e9ecef'
            }}>
              <h3 style={{color: '#2c5530', marginBottom: '15px', fontSize: '1.4em'}}>
                🔬 Scientific Classification
              </h3>
              <div style={{display: 'grid', gap: '10px'}}>
                <div style={{display: 'flex', justifyContent: 'space-between', padding: '8px 0', borderBottom: '1px solid #dee2e6'}}>
                  <strong style={{color: '#495057'}}>Scientific Name:</strong>
                  <span style={{color: '#2c5530', fontWeight: 'bold'}}>{scientificName}</span>
                </div>
                <div style={{display: 'flex', justifyContent: 'space-between', padding: '8px 0'}}>
                  <strong style={{color: '#495057'}}>Local Name:</strong>
                  <span style={{color: '#2c5530', fontWeight: 'bold'}}>{localName}</span>
                </div>
              </div>
            </div>

            {/* Medicinal Properties */}
            <div style={{
              background: '#e8f5e8',
              padding: '20px',
              borderRadius: '10px',
              border: '2px solid #c3e6cb'
            }}>
              <h3 style={{color: '#155724', marginBottom: '15px', fontSize: '1.4em'}}>
                💊 Medicinal Properties & Benefits
              </h3>
              <ul style={{
                listStyle: 'none',
                padding: '0',
                margin: '0'
              }}>
                {arrayDataItems}
              </ul>

              {features.length > 0 && (
                <div style={{
                  marginTop: '20px',
                  padding: '15px',
                  background: '#d4edda',
                  borderRadius: '8px',
                  border: '1px solid #c3e6cb'
                }}>
                  <p style={{
                    margin: '0',
                    fontSize: '0.9em',
                    color: '#155724'
                  }}>
                    <strong>⚠️ Important:</strong> This information is for educational purposes only.
                    Always consult with a healthcare professional before using any medicinal plant for treatment.
                  </p>
                </div>
              )}
            </div>
          </div>

          <div className="left_side" style={{flex: '0 0 400px'}}>
            <div className="img" style={{
              border: '3px solid #2c5530',
              borderRadius: '15px',
              overflow: 'hidden',
              boxShadow: '0 8px 25px rgba(44, 85, 48, 0.3)'
            }}>
              <img
                src={photo}
                alt={scientificName}
                style={{
                  width: '100%',
                  height: '400px',
                  objectFit: 'cover'
                }}
                onError={(e) => {
                  e.target.src = "https://via.placeholder.com/400x400/f8f9fa/6c757d?text=Plant+Image+Not+Available";
                }}
              />
            </div>

            {/* Additional Info */}
            <div style={{
              marginTop: '20px',
              padding: '15px',
              background: '#fff3cd',
              borderRadius: '10px',
              border: '1px solid #ffeaa7'
            }}>
              <h4 style={{color: '#856404', margin: '0 0 10px 0'}}>
                🔍 How to Use This Information
              </h4>
              <ul style={{
                margin: '0',
                paddingLeft: '20px',
                color: '#856404',
                fontSize: '0.9em'
              }}>
                <li>Identify the plant correctly before use</li>
                <li>Research proper dosage and preparation</li>
                <li>Check for allergies or contraindications</li>
                <li>Consult healthcare providers for medical advice</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div className="d-flex justify-content-center">
        <button
          type="button"
          className="btn btn-success btn-lg mt-3 mb-5"
          onClick={handleClick}
        >
          Feedback
        </button>
      </div>

      <Footer />
    </>
  );
};

export default Result;