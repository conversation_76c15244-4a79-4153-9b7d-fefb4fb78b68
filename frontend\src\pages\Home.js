import { useNavigate } from "react-router-dom";
import logo from "../Assets/Logo.png";
import Typewriter from "./Typewriter";

const Home = () => {
  const navigate = useNavigate();
  const handleClick = () => {
    navigate("/dragdrop");
  };
  // Removed unused btn styles - now using inline styles
  return (
    <div className="Home">
      <div className="content">
        <div className="title">
          <h1
            style={{
              textAlign: "center",
              color: "white",
              fontWeight: "bolder",
              fontSize: "4rem",
            }}
          >
            Nature Meets Innovation: Advancing Medicinal Plant Detection
          </h1>
        </div>
        <div className="para">
          <p>
            <Typewriter
              text="The Website aims to develop a machine learning model for the
                identification and classification of medicinal plants from images
                and create a user-friendly application to provide valuable
                information about the medicinal plants."
              delay={60}
            />
          </p>
        </div>
        <div className="foot">
          <div style={{
            display: 'flex',
            gap: '30px',
            justifyContent: 'center',
            alignItems: 'center',
            flexWrap: 'wrap',
            marginTop: '30px'
          }}>
            <button
              type="button"
              onClick={handleClick}
              style={{
                padding: '20px 40px',
                fontSize: '18px',
                fontWeight: 'bold',
                borderRadius: '15px',
                cursor: 'pointer',
                background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                color: 'white',
                border: 'none',
                boxShadow: '0 8px 25px rgba(40, 167, 69, 0.4)',
                transition: 'all 0.3s ease',
                minWidth: '200px'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-3px)';
                e.target.style.boxShadow = '0 12px 35px rgba(40, 167, 69, 0.5)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 8px 25px rgba(40, 167, 69, 0.4)';
              }}
            >
              🚀 Try Original System
            </button>
            <button
              type="button"
              onClick={() => navigate('/dual-search')}
              style={{
                padding: '20px 40px',
                fontSize: '18px',
                fontWeight: 'bold',
                borderRadius: '15px',
                cursor: 'pointer',
                background: 'linear-gradient(135deg, #2c5530 0%, #4a7c59 100%)',
                color: 'white',
                border: 'none',
                boxShadow: '0 8px 25px rgba(44, 85, 48, 0.4)',
                transition: 'all 0.3s ease',
                minWidth: '200px'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-3px)';
                e.target.style.boxShadow = '0 12px 35px rgba(44, 85, 48, 0.5)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 8px 25px rgba(44, 85, 48, 0.4)';
              }}
            >
              🌿 Advanced Search
            </button>
          </div>
          <img src={logo} alt="Logo" style={{ width: 200, height: 200 }} />
        </div>
      </div>
    </div>
  );
};

export default Home;
