import { useNavigate } from "react-router-dom";
import logo from "../Assets/Logo.png";
import Typewriter from "./Typewriter";

const Home = () => {
  const navigate = useNavigate();
  const handleClick = () => {
    navigate("/dragdrop");
  };
  const btn = {
    width: "100px",
    height: "50px",
    fontWeight: "bolder",
  };
  return (
    <div className="Home">
      <div className="content">
        <div className="title">
          <h1
            style={{
              textAlign: "center",
              color: "white",
              fontWeight: "bolder",
              fontSize: "4rem",
            }}
          >
            Nature Meets Innovation: Advancing Medicinal Plant Detection
          </h1>
        </div>
        <div className="para">
          <p>
            <Typewriter
              text="The Website aims to develop a machine learning model for the
                identification and classification of medicinal plants from images
                and create a user-friendly application to provide valuable
                information about the medicinal plants."
              delay={60}
            />
          </p>
        </div>
        <div className="foot">
          <div style={{ display: 'flex', gap: '20px', justifyContent: 'center', alignItems: 'center' }}>
            <button
              type="button"
              className="btn btn-outline-success"
              style={btn}
              onClick={handleClick}
            >
              Try It
            </button>
            <button
              type="button"
              className="btn btn-outline-primary"
              style={{...btn, width: '150px'}}
              onClick={() => navigate('/dual-search')}
            >
              Dual Search
            </button>
          </div>
          <img src={logo} alt="Logo" style={{ width: 200, height: 200 }} />
        </div>
      </div>
    </div>
  );
};

export default Home;
