<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medicinal Plant Information Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .plant-card { border: 2px solid #2c5530; border-radius: 15px; overflow: hidden; margin: 20px 0; }
        .plant-header { background: linear-gradient(135deg, #2c5530 0%, #4a7c59 100%); color: white; padding: 20px; text-align: center; }
        .plant-content { padding: 30px; display: grid; grid-template-columns: 1fr 400px; gap: 30px; }
        .plant-info { display: flex; flex-direction: column; gap: 20px; }
        .info-section { background: #f8f9fa; padding: 20px; border-radius: 10px; border: 2px solid #e9ecef; }
        .medicinal-section { background: #e8f5e8; padding: 20px; border-radius: 10px; border: 2px solid #c3e6cb; }
        .plant-image { border: 3px solid #2c5530; border-radius: 15px; overflow: hidden; }
        .plant-image img { width: 100%; height: 400px; object-fit: cover; }
        .feature-list { list-style: none; padding: 0; margin: 0; }
        .feature-item { padding: 8px 0; border-bottom: 1px solid #eee; font-size: 16px; color: #2c5530; }
        .warning-box { margin-top: 20px; padding: 15px; background: #d4edda; border-radius: 8px; border: 1px solid #c3e6cb; }
        .usage-guide { margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 10px; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌿 Medicinal Plant Information System</h1>
        <p>Testing comprehensive medicinal plant data display</p>
        
        <div class="plant-card">
            <div class="plant-header">
                <h1 style="margin: 0; font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                    🌿 Rosary Pea
                </h1>
                <p style="margin: 10px 0 0 0; font-size: 1.2em; opacity: 0.9;">
                    Medicinal Plant Information & Recommendations
                </p>
            </div>
            
            <div class="plant-content">
                <div class="plant-info">
                    <!-- Scientific Information -->
                    <div class="info-section">
                        <h3 style="color: #2c5530; margin-bottom: 15px; font-size: 1.4em;">
                            🔬 Scientific Classification
                        </h3>
                        <div style="display: grid; gap: 10px;">
                            <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #dee2e6;">
                                <strong style="color: #495057;">Scientific Name:</strong>
                                <span style="color: #2c5530; font-weight: bold;">Abrus precatorius</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; padding: 8px 0;">
                                <strong style="color: #495057;">Local Name:</strong>
                                <span style="color: #2c5530; font-weight: bold;">Rosary Pea</span>
                            </div>
                        </div>
                    </div>

                    <!-- Medicinal Properties -->
                    <div class="medicinal-section">
                        <h3 style="color: #155724; margin-bottom: 15px; font-size: 1.4em;">
                            💊 Medicinal Properties & Benefits
                        </h3>
                        <ul class="feature-list">
                            <li class="feature-item">🌿 Anti-cancer properties</li>
                            <li class="feature-item">🌿 Anti-diabetic effects</li>
                            <li class="feature-item">🌿 Anti-inflammatory action</li>
                            <li class="feature-item">🌿 Antioxidant properties</li>
                        </ul>
                        
                        <div class="warning-box">
                            <p style="margin: 0; font-size: 0.9em; color: #155724;">
                                <strong>⚠️ Important:</strong> This information is for educational purposes only. 
                                Always consult with a healthcare professional before using any medicinal plant for treatment.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div>
                    <div class="plant-image">
                        <img src="https://m.media-amazon.com/images/I/71JLfmyrW3L.jpg" alt="Abrus precatorius">
                    </div>
                    
                    <div class="usage-guide">
                        <h4 style="color: #856404; margin: 0 0 10px 0;">
                            🔍 How to Use This Information
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; color: #856404; font-size: 0.9em;">
                            <li>Identify the plant correctly before use</li>
                            <li>Research proper dosage and preparation</li>
                            <li>Check for allergies or contraindications</li>
                            <li>Consult healthcare providers for medical advice</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test with another plant -->
        <div class="plant-card">
            <div class="plant-header">
                <h1 style="margin: 0; font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                    🌿 Aloe Vera
                </h1>
                <p style="margin: 10px 0 0 0; font-size: 1.2em; opacity: 0.9;">
                    Medicinal Plant Information & Recommendations
                </p>
            </div>
            
            <div class="plant-content">
                <div class="plant-info">
                    <!-- Scientific Information -->
                    <div class="info-section">
                        <h3 style="color: #2c5530; margin-bottom: 15px; font-size: 1.4em;">
                            🔬 Scientific Classification
                        </h3>
                        <div style="display: grid; gap: 10px;">
                            <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #dee2e6;">
                                <strong style="color: #495057;">Scientific Name:</strong>
                                <span style="color: #2c5530; font-weight: bold;">Aloe vera</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; padding: 8px 0;">
                                <strong style="color: #495057;">Local Name:</strong>
                                <span style="color: #2c5530; font-weight: bold;">Aloe</span>
                            </div>
                        </div>
                    </div>

                    <!-- Medicinal Properties -->
                    <div class="medicinal-section">
                        <h3 style="color: #155724; margin-bottom: 15px; font-size: 1.4em;">
                            💊 Medicinal Properties & Benefits
                        </h3>
                        <ul class="feature-list">
                            <li class="feature-item">🌿 Anti-inflammatory action</li>
                            <li class="feature-item">🌿 Wound healing properties</li>
                            <li class="feature-item">🌿 Skin moisturizing effects</li>
                            <li class="feature-item">🌿 Digestive health support</li>
                        </ul>
                        
                        <div class="warning-box">
                            <p style="margin: 0; font-size: 0.9em; color: #155724;">
                                <strong>⚠️ Important:</strong> This information is for educational purposes only. 
                                Always consult with a healthcare professional before using any medicinal plant for treatment.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div>
                    <div class="plant-image">
                        <img src="https://5.imimg.com/data5/OU/HK/MY-13326410/pure-aloe-vera-plant.jpg" alt="Aloe vera">
                    </div>
                    
                    <div class="usage-guide">
                        <h4 style="color: #856404; margin: 0 0 10px 0;">
                            🔍 How to Use This Information
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; color: #856404; font-size: 0.9em;">
                            <li>Identify the plant correctly before use</li>
                            <li>Research proper dosage and preparation</li>
                            <li>Check for allergies or contraindications</li>
                            <li>Consult healthcare providers for medical advice</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e3f2fd; border-radius: 10px; border: 2px solid #90caf9;">
            <h3 style="color: #1565c0; margin-bottom: 15px;">🎯 System Features</h3>
            <ul style="color: #1565c0;">
                <li><strong>Complete Plant Information:</strong> Scientific name, local name, and medicinal features</li>
                <li><strong>Visual Plant Reference:</strong> High-quality images for plant identification</li>
                <li><strong>Safety Guidelines:</strong> Important warnings and usage recommendations</li>
                <li><strong>Professional Layout:</strong> Clean, medical-grade information presentation</li>
                <li><strong>Educational Focus:</strong> Designed for learning and reference purposes</li>
            </ul>
        </div>
    </div>
</body>
</html>
