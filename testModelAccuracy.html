<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Model Accuracy Test</title>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.11.0/dist/tf.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
        #imagePreview { max-width: 300px; max-height: 300px; margin: 10px 0; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .predictions { margin: 10px 0; }
        .prediction-item { margin: 5px 0; padding: 5px; background: #f0f0f0; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Medicinal Plant Model Accuracy Test</h1>
        
        <div class="test-section">
            <h2>📊 Model Status</h2>
            <div id="modelStatus">Loading model...</div>
        </div>
        
        <div class="test-section">
            <h2>🖼️ Image Test</h2>
            <input type="file" id="imageInput" accept="image/*">
            <br>
            <img id="imagePreview" style="display: none;">
            <br>
            <button onclick="testImage()" id="testBtn" disabled>Test Image</button>
            <div id="imageResults"></div>
        </div>
        
        <div class="test-section">
            <h2>🔍 Prediction Analysis</h2>
            <div id="predictionAnalysis"></div>
        </div>
        
        <div class="test-section">
            <h2>📋 Known Issues & Solutions</h2>
            <div id="recommendations"></div>
        </div>
    </div>

    <script>
        // Plant classes array (copy from your project)
        const plantClasses = [
            "Abelmoschus sagittifolius", "Abrus precatorius", "Abutilon indicum", "Acanthus integrifolius",
            "Acorus tatarinowii", "Agave americana", "Ageratum conyzoides", "Allium ramosum",
            "Alocasia macrorrhizos", "Aloe vera", "Alpinia officinarum", "Amomum longiligulare",
            "Ampelopsis cantoniensis", "Andrographis paniculata", "Angelica dahurica", "Ardisia sylvestris",
            "Artemisia vulgaris", "Artocarpus altilis", "Artocarpus heterophyllus", "Artocarpus lakoocha",
            "Asparagus cochinchinensis", "Asparagus officinalis", "Averrhoa carambola", "Baccaurea sp.",
            "Barleria lupulina", "Bengal arum", "Berchemia lineata", "Bidens pilosa",
            "Bischofia trifoliata", "Blackberry lily", "Blumea balsamifera", "Boehmeria nivea",
            "Breynia vitis", "Bryophyllum pinnatum", "Caesalpinia sappan", "Camellia sinensis",
            "Cananga odorata", "Carica papaya", "Cassia alata", "Catharanthus roseus",
            "Centella asiatica", "Chromolaena odorata", "Chrysanthemum morifolium", "Citrus aurantifolia",
            "Citrus hystrix", "Citrus maxima", "Clerodendrum chinense", "Clerodendrum serratum",
            "Glycyrrhiza uralensis Fisch.", "Cocos nucifera", "Coix lacryma-jobi", "Coleus amboinicus",
            "Cordyline fruticosa", "Costus speciosus", "Crescentia cujete L.", "Curcuma longa",
            "Cymbopogon citratus", "Cyperus rotundus", "Datura metel", "Dendrobium nobile",
            "Derris elliptica", "Dimocarpus longan", "Dioscorea persimilis", "Eichhornia crassipes",
            "Eleutherine bulbosa", "Erythrina variegata", "Eupatorium fortunei", "Eupatorium triplinerve",
            "Euphorbia hirta", "Euphorbia pulcherrima", "Euphorbia tirucalli", "Euphorbia tithymaloides",
            "Eurycoma longifolia", "Excoecaria cochinchinensis", "Excoecaria sp", "Fallopia multiflora",
            "Ficus auriculata", "Ficus racemosa", "Fructus lycii", "Glochidion eriocarpum",
            "Glycosmis pentaphylla", "Gonocaryum lobbianum", "Gymnema sylvestre", "Gynura divaricata",
            "Hemerocallis fulva", "Hemigraphis glaucescens", "Hibiscus mutabilis", "Hibiscus rosa-sinensis",
            "Hibiscus sabdariffa", "Holarrhena antidysenterica", "Homalomena occulta", "Houttuynia cordata",
            "Imperata cylindrica", "Iris domestica", "Ixora coccinea", "Jasminum sambac",
            "Jatropha gossypiifolia", "Jatropha multifida", "Jatropha podagrica", "Justicia gendarussa",
            "Kalanchoe pinnata", "Lactuca indica", "Lantana camara", "Lawsonia inermis",
            "Leea rubra", "Litsea glutinosa", "Lonicera dasystyla", "Lpomoea sp",
            "Maesa indica", "Mallotus barbatus", "Mangifera indica", "Melastoma malabathricum",
            "Mentha spicata", "Microcos tomentosa", "Micromelum falcatum", "Millettia pulchra",
            "Mimosa pudica", "Morinda citrifolia", "Moringa oleifera", "Morus alba",
            "Mussaenda philippica", "Nelumbo nucifera", "Ocimum basilicum", "Ocimum gratissimum",
            "Ocimum sanctum", "Oenanthe javanica", "Ophiopogon japonicus", "Paederia lanuginosa",
            "Pandanus amaryllifolius", "Pandanus sp.", "Pandanus tectorius", "Parameria laevigata",
            "Passiflora foetida", "Pereskia sacharosa", "Phyllanthus reticulatus", "Phyllanthus urinaria",
            "Piper betle", "Piper nigrum", "Plantago major", "Plumeria rubra",
            "Polyscias scutellaria", "Premna serratifolia", "Psidium guajava", "Psychotria reevesii Wall.",
            "Punica granatum", "Quisqualis indica", "Rauvolfia serpentina", "Rauvolfia tetraphylla",
            "Rhinacanthus nasutus", "Rhodomyrtus tomentosa", "Ruellia tuberosa", "Sanseviera canaliculata Carr",
            "Holarrhena pubescens", "Sarcandra glabra", "Sauropus androgynus", "Schefflera heptaphylla",
            "Schefflera venulosa", "Senna alata", "Sida acuta Burm.", "Solanum mammosum",
            "Solanum torvum", "Spilanthes acmella", "Spondias dulcis", "Stachytarpheta jamaicensis",
            "Stephania dielsiana", "Stereospermum chelonoides", "Streptocaulon juventas", "Syzygium nervosum",
            "Tabernaemontana divaricata", "Tacca subflabellata", "Tamarindus indica", "Terminalia catappa",
            "Tradescantia discolor", "Trichanthera gigantea", "Vernonia amygdalina", "Vitex negundo",
            "Xanthium strumarium", "Zanthoxylum avicennae", "Zingiber officinale", "Ziziphus mauritiana",
            "Helicteres hirsuta"
        ];

        let model = null;
        
        // Load model
        async function loadModel() {
            try {
                console.log('Loading model...');
                model = await tf.loadGraphModel('/model/model.json');
                console.log('Model loaded successfully');
                console.log('Input shape:', model.inputs[0].shape);
                console.log('Output shape:', model.outputs[0].shape);
                
                document.getElementById('modelStatus').innerHTML = `
                    <div class="success">
                        ✅ Model loaded successfully<br>
                        📊 Input: ${model.inputs[0].shape.join('×')}<br>
                        📊 Output: ${model.outputs[0].shape.join('×')}<br>
                        🌿 Classes: ${plantClasses.length}
                    </div>
                `;
                document.getElementById('testBtn').disabled = false;
            } catch (error) {
                console.error('Error loading model:', error);
                document.getElementById('modelStatus').innerHTML = `
                    <div class="error">❌ Failed to load model: ${error.message}</div>
                `;
            }
        }
        
        // Preprocess image
        function preprocessImage(image) {
            const resized = image.resizeBilinear([128, 128]);
            const normalized = resized.div(255.0);
            resized.dispose();
            return normalized;
        }
        
        // Test image
        async function testImage() {
            const fileInput = document.getElementById('imageInput');
            if (!fileInput.files[0] || !model) return;
            
            const file = fileInput.files[0];
            const resultsDiv = document.getElementById('imageResults');
            const analysisDiv = document.getElementById('predictionAnalysis');
            
            resultsDiv.innerHTML = '<div>🔄 Processing image...</div>';
            
            try {
                // Load and display image
                const img = new Image();
                img.onload = async () => {
                    // Convert to tensor
                    const tensor = tf.browser.fromPixels(img);
                    console.log('Original image shape:', tensor.shape);
                    
                    // Preprocess
                    const processed = preprocessImage(tensor);
                    const batched = processed.expandDims(0);
                    console.log('Processed image shape:', batched.shape);
                    
                    // Predict
                    const predictions = await model.predict(batched).data();
                    console.log('Raw predictions:', Array.from(predictions));
                    
                    // Clean up tensors
                    tensor.dispose();
                    processed.dispose();
                    batched.dispose();
                    
                    // Analyze predictions
                    const predArray = Array.from(predictions);
                    const maxPred = Math.max(...predArray);
                    const maxIndex = predArray.indexOf(maxPred);
                    
                    // Get top 10 predictions
                    const indexed = predArray.map((pred, idx) => ({index: idx, value: pred, name: plantClasses[idx]}));
                    indexed.sort((a, b) => b.value - a.value);
                    const top10 = indexed.slice(0, 10);
                    
                    // Display results
                    resultsDiv.innerHTML = `
                        <div class="result">
                            <h3>🏆 Top Prediction</h3>
                            <strong>${plantClasses[maxIndex]}</strong><br>
                            Confidence: ${(maxPred * 100).toFixed(2)}%<br>
                            Index: ${maxIndex}
                        </div>
                        <div class="predictions">
                            <h3>📊 Top 10 Predictions</h3>
                            ${top10.map((pred, i) => `
                                <div class="prediction-item">
                                    ${i + 1}. <strong>${pred.name}</strong> - ${(pred.value * 100).toFixed(2)}% (Index: ${pred.index})
                                </div>
                            `).join('')}
                        </div>
                    `;
                    
                    // Analysis
                    const confidence = maxPred * 100;
                    let analysisHTML = '<h3>🔍 Analysis</h3>';
                    
                    if (confidence < 10) {
                        analysisHTML += '<div class="error">❌ Very low confidence - model is uncertain</div>';
                    } else if (confidence < 30) {
                        analysisHTML += '<div class="warning">⚠️ Low confidence - prediction may be unreliable</div>';
                    } else if (confidence < 70) {
                        analysisHTML += '<div class="warning">⚠️ Moderate confidence - check if image matches prediction</div>';
                    } else {
                        analysisHTML += '<div class="success">✅ High confidence - prediction likely accurate</div>';
                    }
                    
                    // Check for potential issues
                    const topConfidences = top10.slice(0, 5).map(p => p.value * 100);
                    const confidenceSpread = topConfidences[0] - topConfidences[4];
                    
                    if (confidenceSpread < 5) {
                        analysisHTML += '<div class="warning">⚠️ Multiple similar predictions - model is confused</div>';
                    }
                    
                    analysisDiv.innerHTML = analysisHTML;
                    
                    // Recommendations
                    updateRecommendations(confidence, top10);
                };
                
                img.src = URL.createObjectURL(file);
                document.getElementById('imagePreview').src = img.src;
                document.getElementById('imagePreview').style.display = 'block';
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        function updateRecommendations(confidence, predictions) {
            const recDiv = document.getElementById('recommendations');
            let recommendations = '<h3>💡 Recommendations</h3>';
            
            if (confidence < 30) {
                recommendations += `
                    <div class="warning">
                        <strong>Low Confidence Issues:</strong><br>
                        • Image may not be a medicinal plant<br>
                        • Image quality might be poor<br>
                        • Plant not in training dataset<br>
                        • Model needs retraining with better data
                    </div>
                `;
            }
            
            recommendations += `
                <div class="result">
                    <strong>General Improvements:</strong><br>
                    • Use clear, well-lit images<br>
                    • Focus on distinctive plant features<br>
                    • Ensure plant is centered in image<br>
                    • Verify model was trained on correct dataset<br>
                    • Check if class order matches training data
                </div>
            `;
            
            recDiv.innerHTML = recommendations;
        }
        
        // Handle file input
        document.getElementById('imageInput').addEventListener('change', function(e) {
            if (e.target.files[0]) {
                const img = document.getElementById('imagePreview');
                img.src = URL.createObjectURL(e.target.files[0]);
                img.style.display = 'block';
            }
        });
        
        // Load model on page load
        loadModel();
    </script>
</body>
</html>
