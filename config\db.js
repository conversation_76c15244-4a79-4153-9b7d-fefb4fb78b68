import mongoose from "mongoose";

const connectDB = async () => {
  try {
    if (!process.env.MONGO_URI) {
      console.log("⚠️ MONGO_URI not defined, running without database");
      return;
    }

    const conn = await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000, // Wait 5s before timing out
      autoIndex: true, // Automatically build indexes
    });

    console.log(
      `✅ Connected to MongoDB: ${conn.connection.host}`
    );
  } catch (error) {
    console.error(`❌ MongoDB Connection Error: ${error.message}`);
    console.log("⚠️ Continuing without database - some features may be limited");
    // Don't exit, continue running without database
  }
};

// Handle unexpected errors to prevent crash
process.on("uncaughtException", (error) => {
  console.error(`🔥 Uncaught Exception: ${error.message}`);
  process.exit(1);
});

export default connectDB;
