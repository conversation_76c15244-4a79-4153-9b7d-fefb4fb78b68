import { Route, Routes } from "react-router-dom";
import "./App.css";
import Dragdrop from "./pages/Dragdrop";
import DualSearchSystem from "./pages/DualSearchSystem";
import FeedbackPage from "./pages/FeedbackPage";
import Home from "./pages/Home";
import InternetResult from "./pages/InternetResult";
import Result from "./pages/Result";
import TestCSV from "./pages/TestCSV";

function App() {
  return (
    <>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/result/:slug" element={<Result />} />
        <Route path="/dragdrop" element={<Dragdrop />} />
        <Route path="/feedback" element={<FeedbackPage />} />
        <Route path="/dual-search" element={<DualSearchSystem />} />
        <Route path="/internet-result" element={<InternetResult />} />
      </Routes>
    </>
  );
}

export default App;
