import { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const DualSearchSystem = () => {
  const [activeSection, setActiveSection] = useState('database');
  const [databasePlants, setDatabasePlants] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredPlants, setFilteredPlants] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const navigate = useNavigate();

  // Load database plants function
  const loadDatabasePlants = useCallback(async () => {
    try {
      const response = await fetch('/uploads/plant-features.csv');
      const csvText = await response.text();
      const lines = csvText.split('\n');
      
      const plants = [];
      for (let i = 1; i < lines.length; i++) {
        if (lines[i].trim()) {
          const values = parseCSVLine(lines[i]);
          if (values.length >= 4) {
            plants.push({
              scientificName: values[0],
              localName: values[1],
              features: values[2],
              photo: values[3],
              index: i - 1
            });
          }
        }
      }
      setDatabasePlants(plants);
      setFilteredPlants(plants.slice(0, 20));
    } catch (error) {
      console.error('Error loading database:', error);
    }
  }, []);

  // Load database plants on component mount
  useEffect(() => {
    loadDatabasePlants();
  }, [loadDatabasePlants]);

  // Filter plants based on search
  useEffect(() => {
    if (searchTerm) {
      const filtered = databasePlants.filter(plant =>
        plant.scientificName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.localName.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredPlants(filtered);
    } else {
      setFilteredPlants(databasePlants.slice(0, 20));
    }
  }, [searchTerm, databasePlants]);

  const parseCSVLine = (line) => {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim().replace(/^"|"$/g, ''));
        current = '';
      } else {
        current += char;
      }
    }
    result.push(current.trim().replace(/^"|"$/g, ''));
    return result;
  };

  const handleDatabasePlantSelect = (plant) => {
    navigate(`/result/${encodeURIComponent(plant.scientificName)}`);
  };

  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) {
      setSelectedFile(null);
      return;
    }

    console.log(`📁 File selected: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`);
    setSelectedFile(file);
    setLoading(true);

    try {
      // Add a small delay to show loading UI
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log(`🔍 Checking image: ${file.name} in ${activeSection} section`);
      console.log(`📊 Database has ${databasePlants.length} plants loaded`);

      // ALWAYS check database first, regardless of section
      const match = await checkDatabaseMatch(file);

      if (match) {
        console.log(`✅ Found in plant-features.csv: ${match.scientificName}`);

        // Show success message with dataset info
        const successMessage = `✅ Found in Your Dataset!\n\n` +
              `🌿 Plant: ${match.scientificName}\n` +
              `🏷️ Local Name: ${match.localName}\n` +
              `🎯 Match Type: ${match.matchType}\n` +
              `📊 Confidence: ${match.confidence}%\n\n` +
              `Loading complete medicinal information...`;

        alert(successMessage);

        // Small delay before navigation for better UX
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Navigate to result page with dataset information
        navigate(`/result/${encodeURIComponent(match.scientificName)}`);

      } else {
        console.log(`❌ Image "${file.name}" not found in plant-features.csv`);

        if (activeSection === 'database') {
          // Database section - offer manual selection
          const tryManual = window.confirm(
            `❌ Image "${file.name}" not found in dataset.\n\n` +
            `Your dataset has ${databasePlants.length} plants available.\n\n` +
            `Would you like to manually select the plant?\n\n` +
            `✅ Click OK to browse plants\n` +
            `🌐 Click Cancel to try internet search`
          );

          if (tryManual) {
            alert('📋 Please scroll down and manually select the correct plant from the gallery below.');
          } else {
            console.log(`🌐 Switching to internet search for: ${file.name}`);
            await searchInternetForPlant(file);
          }
        } else {
          // Internet section - search online
          console.log(`🌐 Searching internet for: ${file.name}`);
          await searchInternetForPlant(file);
        }
      }
    } catch (error) {
      console.error('❌ Error processing image:', error);
      alert(`❌ Error processing image: ${error.message}\n\nPlease try manual selection from database.`);
    } finally {
      setLoading(false);
      setSelectedFile(null);
      // Reset file input
      event.target.value = '';
    }
  };

  const checkDatabaseMatch = async (file) => {
    const fileName = file.name.toLowerCase();
    console.log(`🔍 Checking filename: ${fileName} against ${databasePlants.length} plants in dataset`);

    // Check for exact matches first
    for (const plant of databasePlants) {
      if (plant.photo) {
        const dbFileName = plant.photo.split('/').pop().split('?')[0].toLowerCase();
        console.log(`   Comparing with: ${dbFileName}`);

        // Exact filename match
        if (dbFileName === fileName) {
          console.log(`✅ EXACT MATCH found: ${plant.scientificName}`);
          return {
            ...plant,
            matchType: 'Exact filename match',
            confidence: 100
          };
        }
      }
    }

    // Check for partial matches
    for (const plant of databasePlants) {
      if (plant.photo) {
        const dbFileName = plant.photo.split('/').pop().split('?')[0].toLowerCase();
        const fileBase = fileName.split('.')[0];
        const dbBase = dbFileName.split('.')[0];

        // Partial filename match
        if (fileBase.length > 3 && dbBase.includes(fileBase)) {
          console.log(`✅ PARTIAL MATCH found: ${plant.scientificName} (${fileBase} in ${dbBase})`);
          return {
            ...plant,
            matchType: 'Partial filename match',
            confidence: 85
          };
        }

        // Reverse partial match
        if (dbBase.length > 3 && fileBase.includes(dbBase)) {
          console.log(`✅ REVERSE PARTIAL MATCH found: ${plant.scientificName} (${dbBase} in ${fileBase})`);
          return {
            ...plant,
            matchType: 'Reverse partial match',
            confidence: 80
          };
        }
      }
    }

    // Check for similar patterns (like "71JLfmyrW3L" variations)
    for (const plant of databasePlants) {
      if (plant.photo) {
        const dbFileName = plant.photo.split('/').pop().split('?')[0].toLowerCase();
        const fileBase = fileName.split('.')[0];
        const dbBase = dbFileName.split('.')[0];

        // Check for Amazon-style image names (like 71JLfmyrW3L)
        const amazonPattern = /^[0-9][0-9a-z]+$/i;
        if (amazonPattern.test(fileBase) && amazonPattern.test(dbBase)) {
          if (fileBase.substring(0, 5) === dbBase.substring(0, 5)) {
            console.log(`✅ PATTERN MATCH found: ${plant.scientificName} (Amazon-style: ${fileBase} ~ ${dbBase})`);
            return {
              ...plant,
              matchType: 'Pattern similarity match',
              confidence: 75
            };
          }
        }
      }
    }

    console.log(`❌ No match found for: ${fileName}`);
    return null;
  };

  const searchInternetForPlant = async (file) => {
    try {
      setLoading(true);
      
      const searchResults = await simulateInternetSearch(file);
      
      if (searchResults && searchResults.length > 0) {
        const topResult = searchResults[0];
        
        const useResult = window.confirm(
          `🌐 Internet Search Result:\n\nPlant: ${topResult.name}\nConfidence: ${topResult.confidence}%\nSource: ${topResult.source}\n\nUse this result?`
        );
        
        if (useResult) {
          navigate(`/internet-result`, { 
            state: { 
              plantData: topResult,
              source: 'internet'
            }
          });
        }
      } else {
        alert('❌ No results found online. Please try manual selection from database.');
        setActiveSection('database');
      }
    } catch (error) {
      console.error('Internet search error:', error);
      alert('❌ Internet search failed. Please try manual selection from database.');
      setActiveSection('database');
    } finally {
      setLoading(false);
    }
  };

  const simulateInternetSearch = async (file) => {
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const mockResults = [
      {
        name: 'Unknown Plant Species',
        scientificName: 'Plantae species',
        localName: 'Common Plant',
        features: 'General plant properties, photosynthesis, oxygen production',
        confidence: 75,
        source: 'Internet Plant Database',
        photo: 'https://via.placeholder.com/400x300/4CAF50/white?text=Internet+Search+Result'
      }
    ];
    
    return mockResults;
  };

  return (
    <>
      <style>
        {`
          @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
          }
          @keyframes loading {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
          }
        `}
      </style>
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%)',
        padding: '0'
      }}>
      {/* Header */}
      <div style={{
        background: 'linear-gradient(135deg, #2c5530 0%, #4a7c59 100%)',
        color: 'white',
        padding: '40px 20px',
        textAlign: 'center',
        boxShadow: '0 4px 20px rgba(44, 85, 48, 0.3)'
      }}>
        <h1 style={{ 
          margin: '0 0 10px 0', 
          fontSize: '3em', 
          fontWeight: 'bold',
          textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
        }}>
          🌿 Medicinal Plant Recognition System
        </h1>
        <p style={{ 
          margin: '0', 
          fontSize: '1.3em', 
          opacity: '0.9',
          fontWeight: '300'
        }}>
          Advanced Plant Identification & Therapeutic Information Database
        </p>
      </div>

      <div style={{ padding: '40px 20px', maxWidth: '1400px', margin: '0 auto' }}>
        {/* Section Selector */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          marginBottom: '40px',
          gap: '30px',
          flexWrap: 'wrap'
        }}>
          <button
            onClick={() => setActiveSection('database')}
            style={{
              padding: '25px 45px',
              fontSize: '20px',
              fontWeight: 'bold',
              borderRadius: '15px',
              cursor: 'pointer',
              background: activeSection === 'database' 
                ? 'linear-gradient(135deg, #2c5530 0%, #4a7c59 100%)' 
                : 'white',
              color: activeSection === 'database' ? 'white' : '#2c5530',
              border: '3px solid #2c5530',
              boxShadow: activeSection === 'database' 
                ? '0 8px 25px rgba(44, 85, 48, 0.4)' 
                : '0 4px 15px rgba(44, 85, 48, 0.2)',
              transition: 'all 0.3s ease',
              transform: activeSection === 'database' ? 'translateY(-2px)' : 'none',
              minWidth: '280px'
            }}
          >
            📚 Database Search
            <div style={{ fontSize: '14px', fontWeight: 'normal', marginTop: '8px', opacity: '0.9' }}>
              200 Verified Medicinal Plants
            </div>
          </button>
          <button
            onClick={() => setActiveSection('internet')}
            style={{
              padding: '25px 45px',
              fontSize: '20px',
              fontWeight: 'bold',
              borderRadius: '15px',
              cursor: 'pointer',
              background: activeSection === 'internet' 
                ? 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)' 
                : 'white',
              color: activeSection === 'internet' ? 'white' : '#007bff',
              border: '3px solid #007bff',
              boxShadow: activeSection === 'internet' 
                ? '0 8px 25px rgba(0, 123, 255, 0.4)' 
                : '0 4px 15px rgba(0, 123, 255, 0.2)',
              transition: 'all 0.3s ease',
              transform: activeSection === 'internet' ? 'translateY(-2px)' : 'none',
              minWidth: '280px'
            }}
          >
            🌐 Internet Search
            <div style={{ fontSize: '14px', fontWeight: 'normal', marginTop: '8px', opacity: '0.9' }}>
              Global Plant Database
            </div>
          </button>
        </div>

        {/* Database Section */}
        {activeSection === 'database' && (
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '0',
            boxShadow: '0 10px 40px rgba(44, 85, 48, 0.15)',
            border: '1px solid rgba(44, 85, 48, 0.1)',
            overflow: 'hidden'
          }}>
            <div style={{
              background: 'linear-gradient(135deg, #2c5530 0%, #4a7c59 100%)',
              color: 'white',
              padding: '30px',
              textAlign: 'center'
            }}>
              <h2 style={{ 
                margin: '0 0 10px 0', 
                fontSize: '2.2em',
                fontWeight: 'bold',
                textShadow: '1px 1px 3px rgba(0,0,0,0.3)'
              }}>
                📚 Medicinal Plant Database
              </h2>
              <p style={{ 
                margin: '0', 
                fontSize: '1.1em', 
                opacity: '0.9' 
              }}>
                200 Verified Medicinal Plants with Complete Therapeutic Information
              </p>
            </div>

            <div style={{ padding: '40px' }}>
              {/* Upload Section */}
              <div style={{
                background: 'linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%)',
                padding: '30px',
                borderRadius: '15px',
                marginBottom: '30px',
                border: '2px dashed #2c5530',
                textAlign: 'center'
              }}>
                <h3 style={{ color: '#155724', marginBottom: '20px', fontSize: '1.5em' }}>
                  📸 Upload Plant Image
                </h3>
                <div style={{
                  position: 'relative',
                  display: 'inline-block',
                  width: '100%',
                  maxWidth: '400px'
                }}>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    style={{
                      padding: '15px',
                      border: '2px solid #2c5530',
                      borderRadius: '10px',
                      marginBottom: '15px',
                      fontSize: '16px',
                      width: '100%',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.borderColor = '#4a7c59';
                      e.target.style.backgroundColor = '#f8f9fa';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.borderColor = '#2c5530';
                      e.target.style.backgroundColor = 'white';
                    }}
                  />
                </div>
                {selectedFile && (
                  <div style={{
                    background: '#d4edda',
                    border: '1px solid #c3e6cb',
                    borderRadius: '8px',
                    padding: '10px',
                    marginBottom: '15px',
                    color: '#155724'
                  }}>
                    <strong>📁 File Selected:</strong> {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
                  </div>
                )}
                <p style={{ margin: '15px 0 0 0', color: '#155724', fontSize: '1em' }}>
                  Upload images like "71JLfmyrW3L.jpg" from your medicinal plant dataset
                </p>
              </div>

              {/* Manual Search */}
              <div style={{
                background: '#fff',
                padding: '30px',
                borderRadius: '15px',
                border: '2px solid #2c5530'
              }}>
                <h3 style={{ color: '#2c5530', marginBottom: '20px', fontSize: '1.5em' }}>
                  🔍 Browse & Search Plants
                </h3>
                <input
                  type="text"
                  placeholder="Search by scientific name or local name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '15px',
                    border: '2px solid #2c5530',
                    borderRadius: '10px',
                    fontSize: '16px',
                    marginBottom: '25px'
                  }}
                />
                
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill, minmax(320px, 1fr))',
                  gap: '20px',
                  maxHeight: '500px',
                  overflowY: 'auto'
                }}>
                  {filteredPlants.map((plant, index) => (
                    <div
                      key={index}
                      onClick={() => handleDatabasePlantSelect(plant)}
                      style={{
                        border: '2px solid #2c5530',
                        borderRadius: '15px',
                        padding: '20px',
                        cursor: 'pointer',
                        background: '#fff',
                        transition: 'all 0.3s ease',
                        boxShadow: '0 4px 15px rgba(44, 85, 48, 0.1)'
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.background = '#e8f5e8';
                        e.target.style.transform = 'scale(1.02)';
                        e.target.style.boxShadow = '0 8px 25px rgba(44, 85, 48, 0.2)';
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.background = '#fff';
                        e.target.style.transform = 'scale(1)';
                        e.target.style.boxShadow = '0 4px 15px rgba(44, 85, 48, 0.1)';
                      }}
                    >
                      <img
                        src={plant.photo}
                        alt={plant.scientificName}
                        style={{
                          width: '100%',
                          height: '180px',
                          objectFit: 'cover',
                          borderRadius: '10px',
                          marginBottom: '15px'
                        }}
                        onError={(e) => {
                          e.target.src = 'https://via.placeholder.com/320x180/f8f9fa/6c757d?text=Image+Not+Available';
                        }}
                      />
                      <h4 style={{ margin: '0 0 8px 0', color: '#2c5530', fontSize: '1.1em' }}>
                        {plant.scientificName}
                      </h4>
                      <p style={{ margin: '0 0 8px 0', color: '#666', fontSize: '1em', fontWeight: 'bold' }}>
                        {plant.localName}
                      </p>
                      <p style={{ margin: '0', color: '#155724', fontSize: '0.9em' }}>
                        {plant.features.substring(0, 60)}...
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Internet Section */}
        {activeSection === 'internet' && (
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '0',
            boxShadow: '0 10px 40px rgba(0, 123, 255, 0.15)',
            border: '1px solid rgba(0, 123, 255, 0.1)',
            overflow: 'hidden'
          }}>
            <div style={{
              background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
              color: 'white',
              padding: '30px',
              textAlign: 'center'
            }}>
              <h2 style={{ 
                margin: '0 0 10px 0', 
                fontSize: '2.2em',
                fontWeight: 'bold',
                textShadow: '1px 1px 3px rgba(0,0,0,0.3)'
              }}>
                🌐 Internet Plant Search
              </h2>
              <p style={{ 
                margin: '0', 
                fontSize: '1.1em', 
                opacity: '0.9' 
              }}>
                Search Global Plant Databases for Unknown Species
              </p>
            </div>
            
            <div style={{ padding: '40px' }}>
              <div style={{
                background: 'linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%)',
                padding: '30px',
                borderRadius: '15px',
                border: '2px dashed #007bff',
                textAlign: 'center'
              }}>
                <h3 style={{ color: '#1565c0', marginBottom: '20px', fontSize: '1.5em' }}>
                  📸 Upload Any Plant Image
                </h3>
                <div style={{
                  position: 'relative',
                  display: 'inline-block',
                  width: '100%',
                  maxWidth: '400px'
                }}>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    style={{
                      padding: '15px',
                      border: '2px solid #007bff',
                      borderRadius: '10px',
                      marginBottom: '15px',
                      fontSize: '16px',
                      width: '100%',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.borderColor = '#0056b3';
                      e.target.style.backgroundColor = '#f8f9fa';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.borderColor = '#007bff';
                      e.target.style.backgroundColor = 'white';
                    }}
                  />
                </div>
                {selectedFile && (
                  <div style={{
                    background: '#d4edda',
                    border: '1px solid #c3e6cb',
                    borderRadius: '8px',
                    padding: '10px',
                    marginBottom: '15px',
                    color: '#155724'
                  }}>
                    <strong>📁 File Selected:</strong> {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
                  </div>
                )}
                <div style={{
                  background: '#e8f5e8',
                  padding: '15px',
                  borderRadius: '10px',
                  margin: '15px 0',
                  border: '1px solid #c3e6cb'
                }}>
                  <p style={{ margin: '0', color: '#155724', fontSize: '1em', fontWeight: 'bold' }}>
                    🔍 Smart Search Process:
                  </p>
                  <p style={{ margin: '5px 0 0 0', color: '#155724', fontSize: '0.9em' }}>
                    1. First checks your plant-features.csv dataset<br/>
                    2. If found: Shows complete medicinal information<br/>
                    3. If not found: Searches global internet databases
                  </p>
                </div>
                <p style={{ margin: '15px 0 0 0', color: '#1565c0', fontSize: '1em' }}>
                  Upload images like "71JLfmyrW3L.jpg" - system will find them in your dataset!
                </p>
              </div>

              <div style={{
                marginTop: '30px',
                padding: '25px',
                background: '#fff3cd',
                borderRadius: '15px',
                border: '1px solid #ffeaa7'
              }}>
                <h4 style={{ color: '#856404', margin: '0 0 15px 0', fontSize: '1.3em' }}>
                  🔍 How Smart Search Works
                </h4>
                <ul style={{ margin: '0', paddingLeft: '25px', color: '#856404', fontSize: '1em' }}>
                  <li><strong>Step 1:</strong> Upload any plant image (even from your dataset)</li>
                  <li><strong>Step 2:</strong> System checks your plant-features.csv first</li>
                  <li><strong>Step 3:</strong> If found in dataset → Shows complete medicinal info</li>
                  <li><strong>Step 4:</strong> If not found → Searches online plant databases</li>
                  <li><strong>Step 5:</strong> Returns best match with confidence score and source</li>
                </ul>

                <div style={{
                  marginTop: '15px',
                  padding: '15px',
                  background: '#d4edda',
                  borderRadius: '8px',
                  border: '1px solid #c3e6cb'
                }}>
                  <p style={{ margin: '0', color: '#155724', fontSize: '0.9em' }}>
                    <strong>💡 Pro Tip:</strong> Your dataset images (like "71JLfmyrW3L.jpg") will be found
                    instantly with complete medicinal information, even when uploaded through this internet section!
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Loading Overlay */}
        {loading && (
          <div style={{
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            background: 'rgba(0,0,0,0.9)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: '10000'
          }}>
            <div style={{
              background: 'white',
              padding: '50px',
              borderRadius: '20px',
              textAlign: 'center',
              boxShadow: '0 20px 60px rgba(0,0,0,0.4)',
              maxWidth: '400px',
              width: '90%'
            }}>
              <div style={{
                fontSize: '64px',
                marginBottom: '25px',
                animation: 'pulse 1.5s infinite'
              }}>🔍</div>
              <div style={{
                fontSize: '24px',
                color: '#2c5530',
                fontWeight: 'bold',
                marginBottom: '15px'
              }}>
                Processing Your Image...
              </div>
              <div style={{
                fontSize: '16px',
                color: '#666',
                marginBottom: '20px'
              }}>
                {activeSection === 'database'
                  ? `Checking ${databasePlants.length} plants in your dataset...`
                  : 'Checking dataset first, then searching internet...'}
              </div>
              <div style={{
                width: '100%',
                height: '4px',
                background: '#e0e0e0',
                borderRadius: '2px',
                overflow: 'hidden'
              }}>
                <div style={{
                  width: '100%',
                  height: '100%',
                  background: 'linear-gradient(90deg, #2c5530, #4a7c59)',
                  animation: 'loading 2s infinite'
                }}></div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
    </>
  );
};

export default DualSearchSystem;
