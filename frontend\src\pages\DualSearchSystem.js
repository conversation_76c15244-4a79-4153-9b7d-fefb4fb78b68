import { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const DualSearchSystem = () => {
  const [activeSection, setActiveSection] = useState('database');
  const [databasePlants, setDatabasePlants] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredPlants, setFilteredPlants] = useState([]);
  // const [selectedImage, setSelectedImage] = useState(null); // Removed unused state
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  // Load database plants function
  const loadDatabasePlants = useCallback(async () => {
    try {
      const response = await fetch('/uploads/plant-features.csv');
      const csvText = await response.text();
      const lines = csvText.split('\n');
      
      const plants = [];
      for (let i = 1; i < lines.length; i++) {
        if (lines[i].trim()) {
          const values = parseCSVLine(lines[i]);
          if (values.length >= 4) {
            plants.push({
              scientificName: values[0],
              localName: values[1],
              features: values[2],
              photo: values[3],
              index: i - 1
            });
          }
        }
      }
      setDatabasePlants(plants);
      setFilteredPlants(plants.slice(0, 20));
    } catch (error) {
      console.error('Error loading database:', error);
    }
  }, []);

  // Load database plants on component mount
  useEffect(() => {
    loadDatabasePlants();
  }, [loadDatabasePlants]);

  // Filter plants based on search
  useEffect(() => {
    if (searchTerm) {
      const filtered = databasePlants.filter(plant =>
        plant.scientificName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.localName.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredPlants(filtered);
    } else {
      setFilteredPlants(databasePlants.slice(0, 20)); // Show first 20 by default
    }
  }, [searchTerm, databasePlants]);

  const parseCSVLine = (line) => {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim().replace(/^"|"$/g, ''));
        current = '';
      } else {
        current += char;
      }
    }
    result.push(current.trim().replace(/^"|"$/g, ''));
    return result;
  };

  const handleDatabasePlantSelect = (plant) => {
    navigate(`/result/${encodeURIComponent(plant.scientificName)}`);
  };

  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // setSelectedImage(file); // Removed unused state
    setLoading(true);

    try {
      // Check if image matches database
      const match = await checkDatabaseMatch(file);
      
      if (match) {
        alert(`✅ Found in Database!\n\nPlant: ${match.scientificName}\nMatch Type: ${match.matchType}`);
        navigate(`/result/${encodeURIComponent(match.scientificName)}`);
      } else {
        // Not in database - offer internet search
        const searchOnline = window.confirm(
          `❌ Plant not found in database.\n\nWould you like to search online for this plant?\n\nClick OK to search internet, Cancel to try manual selection.`
        );
        
        if (searchOnline) {
          await searchInternetForPlant(file);
        } else {
          // Show manual selection
          setActiveSection('database');
          alert('Please manually select the plant from the database section.');
        }
      }
    } catch (error) {
      console.error('Error processing image:', error);
      alert('Error processing image. Please try manual selection.');
    } finally {
      setLoading(false);
    }
  };

  const checkDatabaseMatch = async (file) => {
    const fileName = file.name.toLowerCase();
    
    // Check for filename matches in database
    for (const plant of databasePlants) {
      if (plant.photo) {
        const dbFileName = plant.photo.split('/').pop().split('?')[0].toLowerCase();
        
        if (dbFileName === fileName) {
          return {
            ...plant,
            matchType: 'Exact filename match',
            confidence: 100
          };
        }
        
        // Check partial match
        const fileBase = fileName.split('.')[0];
        const dbBase = dbFileName.split('.')[0];
        
        if (fileBase.length > 3 && dbBase.includes(fileBase)) {
          return {
            ...plant,
            matchType: 'Partial filename match',
            confidence: 85
          };
        }
      }
    }
    
    return null;
  };

  const searchInternetForPlant = async (file) => {
    try {
      setLoading(true);
      
      // Simulate internet search (replace with actual API)
      const searchResults = await simulateInternetSearch(file);
      
      if (searchResults && searchResults.length > 0) {
        const topResult = searchResults[0];
        
        const useResult = window.confirm(
          `🌐 Internet Search Result:\n\nPlant: ${topResult.name}\nConfidence: ${topResult.confidence}%\nSource: ${topResult.source}\n\nUse this result?`
        );
        
        if (useResult) {
          // Create temporary result for internet search
          navigate(`/internet-result`, { 
            state: { 
              plantData: topResult,
              source: 'internet'
            }
          });
        }
      } else {
        alert('❌ No results found online. Please try manual selection from database.');
        setActiveSection('database');
      }
    } catch (error) {
      console.error('Internet search error:', error);
      alert('❌ Internet search failed. Please try manual selection from database.');
      setActiveSection('database');
    } finally {
      setLoading(false);
    }
  };

  const simulateInternetSearch = async (file) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate search results (replace with actual plant identification API)
    const mockResults = [
      {
        name: 'Unknown Plant Species',
        scientificName: 'Plantae species',
        localName: 'Common Plant',
        features: 'General plant properties, photosynthesis, oxygen production',
        confidence: 75,
        source: 'Internet Plant Database',
        photo: 'https://via.placeholder.com/400x300/4CAF50/white?text=Internet+Search+Result'
      }
    ];
    
    return mockResults;
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1 style={{ textAlign: 'center', color: '#2c5530', marginBottom: '30px' }}>
        🌿 Dual Plant Search System
      </h1>
      
      {/* Section Selector */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        marginBottom: '30px',
        gap: '20px'
      }}>
        <button
          onClick={() => setActiveSection('database')}
          style={{
            padding: '15px 30px',
            fontSize: '18px',
            borderRadius: '10px',
            cursor: 'pointer',
            background: activeSection === 'database' ? '#2c5530' : '#f8f9fa',
            color: activeSection === 'database' ? 'white' : '#2c5530',
            border: '2px solid #2c5530'
          }}
        >
          📚 Database Search
        </button>
        <button
          onClick={() => setActiveSection('internet')}
          style={{
            padding: '15px 30px',
            fontSize: '18px',
            borderRadius: '10px',
            cursor: 'pointer',
            background: activeSection === 'internet' ? '#007bff' : '#f8f9fa',
            color: activeSection === 'internet' ? 'white' : '#007bff',
            border: '2px solid #007bff'
          }}
        >
          🌐 Internet Search
        </button>
      </div>

      {/* Database Section */}
      {activeSection === 'database' && (
        <div style={{
          border: '3px solid #2c5530',
          borderRadius: '15px',
          padding: '30px',
          background: '#f8fff8'
        }}>
          <h2 style={{ color: '#2c5530', marginBottom: '20px' }}>
            📚 Search Your Plant Database (200 Plants)
          </h2>
          
          {/* Upload Section */}
          <div style={{
            background: '#e8f5e8',
            padding: '20px',
            borderRadius: '10px',
            marginBottom: '20px',
            border: '2px dashed #2c5530'
          }}>
            <h3 style={{ color: '#155724', marginBottom: '15px' }}>
              📸 Upload Image from Your Dataset
            </h3>
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              style={{
                padding: '10px',
                border: '2px solid #2c5530',
                borderRadius: '5px',
                marginBottom: '10px'
              }}
            />
            <p style={{ margin: '10px 0 0 0', color: '#155724', fontSize: '0.9em' }}>
              Upload images like "71JLfmyrW3L.jpg" that exist in your plant-features.csv
            </p>
          </div>

          {/* Manual Search */}
          <div style={{
            background: '#fff',
            padding: '20px',
            borderRadius: '10px',
            border: '2px solid #2c5530'
          }}>
            <h3 style={{ color: '#2c5530', marginBottom: '15px' }}>
              🔍 Manual Plant Search
            </h3>
            <input
              type="text"
              placeholder="Search by scientific name or local name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #2c5530',
                borderRadius: '5px',
                fontSize: '16px',
                marginBottom: '20px'
              }}
            />
            
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
              gap: '15px',
              maxHeight: '400px',
              overflowY: 'auto'
            }}>
              {filteredPlants.map((plant, index) => (
                <div
                  key={index}
                  onClick={() => handleDatabasePlantSelect(plant)}
                  style={{
                    border: '2px solid #2c5530',
                    borderRadius: '10px',
                    padding: '15px',
                    cursor: 'pointer',
                    background: '#fff',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = '#e8f5e8';
                    e.target.style.transform = 'scale(1.02)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = '#fff';
                    e.target.style.transform = 'scale(1)';
                  }}
                >
                  <img
                    src={plant.photo}
                    alt={plant.scientificName}
                    style={{
                      width: '100%',
                      height: '150px',
                      objectFit: 'cover',
                      borderRadius: '5px',
                      marginBottom: '10px'
                    }}
                    onError={(e) => {
                      e.target.src = 'https://via.placeholder.com/300x150/f8f9fa/6c757d?text=Image+Not+Available';
                    }}
                  />
                  <h4 style={{ margin: '0 0 5px 0', color: '#2c5530' }}>
                    {plant.scientificName}
                  </h4>
                  <p style={{ margin: '0 0 5px 0', color: '#666', fontSize: '0.9em' }}>
                    {plant.localName}
                  </p>
                  <p style={{ margin: '0', color: '#155724', fontSize: '0.8em' }}>
                    {plant.features.substring(0, 50)}...
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Internet Section */}
      {activeSection === 'internet' && (
        <div style={{
          border: '3px solid #007bff',
          borderRadius: '15px',
          padding: '30px',
          background: '#f8fbff'
        }}>
          <h2 style={{ color: '#007bff', marginBottom: '20px' }}>
            🌐 Internet Plant Search
          </h2>
          
          <div style={{
            background: '#e3f2fd',
            padding: '20px',
            borderRadius: '10px',
            border: '2px dashed #007bff'
          }}>
            <h3 style={{ color: '#1565c0', marginBottom: '15px' }}>
              📸 Upload Any Plant Image
            </h3>
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              style={{
                padding: '10px',
                border: '2px solid #007bff',
                borderRadius: '5px',
                marginBottom: '10px'
              }}
            />
            <p style={{ margin: '10px 0 0 0', color: '#1565c0', fontSize: '0.9em' }}>
              Upload any plant image. If not found in database, will search internet.
            </p>
          </div>

          <div style={{
            marginTop: '20px',
            padding: '20px',
            background: '#fff3cd',
            borderRadius: '10px',
            border: '1px solid #ffeaa7'
          }}>
            <h4 style={{ color: '#856404', margin: '0 0 10px 0' }}>
              🔍 How Internet Search Works
            </h4>
            <ul style={{ margin: '0', paddingLeft: '20px', color: '#856404' }}>
              <li>Upload any plant image</li>
              <li>System first checks your database</li>
              <li>If not found, searches online plant databases</li>
              <li>Returns best match with confidence score</li>
              <li>Shows source and reliability information</li>
            </ul>
          </div>
        </div>
      )}

      {loading && (
        <div style={{
          position: 'fixed',
          top: '0',
          left: '0',
          width: '100%',
          height: '100%',
          background: 'rgba(0,0,0,0.8)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: '10000'
        }}>
          <div style={{
            background: 'white',
            padding: '30px',
            borderRadius: '10px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '15px' }}>🔍</div>
            <div style={{ fontSize: '18px', color: '#2c5530' }}>
              {activeSection === 'database' ? 'Checking database...' : 'Searching internet...'}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DualSearchSystem;
