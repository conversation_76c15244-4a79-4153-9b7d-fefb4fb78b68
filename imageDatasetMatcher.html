<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Dataset Matcher</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .upload-section { margin: 20px 0; padding: 20px; border: 2px dashed #007bff; border-radius: 8px; text-align: center; }
        .result-section { margin: 20px 0; padding: 20px; border: 2px solid #28a745; border-radius: 8px; }
        .match-found { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .no-match { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
        button { padding: 12px 24px; margin: 10px 5px; cursor: pointer; border: none; border-radius: 5px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        #imagePreview { max-width: 300px; max-height: 300px; margin: 10px; border: 2px solid #ddd; }
        .plant-card { display: grid; grid-template-columns: 200px 1fr; gap: 20px; align-items: start; }
        .dataset-image { max-width: 180px; max-height: 180px; border: 2px solid #ddd; border-radius: 5px; }
        .plant-details { padding: 15px; background: #f8f9fa; border-radius: 5px; }
        .similarity-score { font-weight: bold; color: #007bff; }
        .dataset-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
        .dataset-item { padding: 10px; border: 1px solid #ddd; border-radius: 5px; text-align: center; }
        .dataset-item img { max-width: 100%; height: 150px; object-fit: cover; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Image Dataset Matcher</h1>
        <p>Upload an image to find if it matches any plant in your dataset</p>
        
        <div class="upload-section">
            <h2>📸 Upload Image</h2>
            <input type="file" id="imageInput" accept="image/*">
            <br>
            <img id="imagePreview" style="display: none;">
            <br>
            <button onclick="findMatches()" id="findBtn" class="btn-primary" disabled>Find Dataset Matches</button>
            <button onclick="showAllDataset()" class="btn-success">Show All Dataset Images</button>
        </div>
        
        <div id="resultsSection"></div>
        
        <div id="datasetSection"></div>
    </div>

    <script>
        let uploadedImage = null;
        let plantDataset = [];
        
        // Load plant dataset from CSV
        async function loadPlantDataset() {
            try {
                const response = await fetch('/uploads/plant-features.csv');
                const csvText = await response.text();
                const lines = csvText.split('\n');
                const headers = lines[0].split(',');
                
                plantDataset = [];
                for (let i = 1; i < lines.length; i++) {
                    if (lines[i].trim()) {
                        const values = parseCSVLine(lines[i]);
                        if (values.length >= 4) {
                            plantDataset.push({
                                scientificName: values[0],
                                localName: values[1],
                                features: values[2],
                                photo: values[3],
                                index: i - 1
                            });
                        }
                    }
                }
                console.log(`Loaded ${plantDataset.length} plants from dataset`);
            } catch (error) {
                console.error('Error loading dataset:', error);
            }
        }
        
        // Parse CSV line handling quoted values
        function parseCSVLine(line) {
            const result = [];
            let current = '';
            let inQuotes = false;
            
            for (let i = 0; i < line.length; i++) {
                const char = line[i];
                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    result.push(current.trim());
                    current = '';
                } else {
                    current += char;
                }
            }
            result.push(current.trim());
            return result;
        }
        
        // Find matches for uploaded image
        async function findMatches() {
            if (!uploadedImage) {
                alert('Please upload an image first');
                return;
            }
            
            const resultsDiv = document.getElementById('resultsSection');
            resultsDiv.innerHTML = '<div class="info">🔍 Searching for matches in dataset...</div>';
            
            try {
                // Method 1: Check if uploaded image URL matches any dataset URLs
                const urlMatches = findURLMatches();
                
                // Method 2: Visual similarity check (simplified)
                const visualMatches = await findVisualMatches();
                
                // Method 3: Filename similarity
                const filenameMatches = findFilenameMatches();
                
                displayResults(urlMatches, visualMatches, filenameMatches);
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="no-match">❌ Error finding matches: ${error.message}</div>`;
            }
        }
        
        // Find URL matches
        function findURLMatches() {
            const matches = [];
            const uploadedFileName = document.getElementById('imageInput').files[0]?.name;
            
            plantDataset.forEach(plant => {
                if (plant.photo && uploadedFileName) {
                    // Extract filename from URL
                    const urlFileName = plant.photo.split('/').pop().split('?')[0];
                    
                    if (urlFileName.toLowerCase() === uploadedFileName.toLowerCase()) {
                        matches.push({
                            plant: plant,
                            matchType: 'Exact filename match',
                            confidence: 100
                        });
                    }
                }
            });
            
            return matches;
        }
        
        // Find filename similarity matches
        function findFilenameMatches() {
            const matches = [];
            const uploadedFileName = document.getElementById('imageInput').files[0]?.name?.toLowerCase();
            
            if (!uploadedFileName) return matches;
            
            plantDataset.forEach(plant => {
                if (plant.photo) {
                    const urlFileName = plant.photo.split('/').pop().split('?')[0].toLowerCase();
                    
                    // Check for partial matches
                    if (urlFileName.includes(uploadedFileName.split('.')[0]) || 
                        uploadedFileName.includes(urlFileName.split('.')[0])) {
                        matches.push({
                            plant: plant,
                            matchType: 'Filename similarity',
                            confidence: 75
                        });
                    }
                }
            });
            
            return matches;
        }
        
        // Simplified visual matching (placeholder for more advanced matching)
        async function findVisualMatches() {
            // This is a simplified version - in a real implementation,
            // you would use image comparison algorithms
            return [];
        }
        
        // Display results
        function displayResults(urlMatches, visualMatches, filenameMatches) {
            const resultsDiv = document.getElementById('resultsSection');
            
            const allMatches = [...urlMatches, ...visualMatches, ...filenameMatches];
            
            if (allMatches.length === 0) {
                resultsDiv.innerHTML = `
                    <div class="no-match">
                        <h3>❌ No Direct Matches Found</h3>
                        <p>Your uploaded image doesn't directly match any images in the dataset.</p>
                        <p>This could mean:</p>
                        <ul>
                            <li>The image is not from your dataset</li>
                            <li>The filename has been changed</li>
                            <li>The image is a different version/crop of a dataset image</li>
                        </ul>
                        <button onclick="showSimilarPlants()" class="btn-primary">Show Similar Plants</button>
                    </div>
                `;
                return;
            }
            
            let html = '<div class="result-section"><h2>✅ Matches Found!</h2>';
            
            // Sort by confidence
            allMatches.sort((a, b) => b.confidence - a.confidence);
            
            allMatches.forEach(match => {
                html += `
                    <div class="match-found">
                        <div class="plant-card">
                            <div>
                                <img src="${match.plant.photo}" alt="${match.plant.scientificName}" class="dataset-image" onerror="this.src='https://via.placeholder.com/180x180/f8f9fa/6c757d?text=Image+Not+Found'">
                                <div class="similarity-score">${match.confidence}% Match</div>
                                <div style="font-size: 0.9em; color: #666;">${match.matchType}</div>
                            </div>
                            <div class="plant-details">
                                <h3>${match.plant.scientificName}</h3>
                                <p><strong>Local Name:</strong> ${match.plant.localName}</p>
                                <p><strong>Medicinal Features:</strong> ${match.plant.features}</p>
                                <p><strong>Dataset Index:</strong> ${match.plant.index}</p>
                                <button onclick="useThisPlant('${match.plant.scientificName}')" class="btn-success">Use This Plant</button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }
        
        // Use selected plant in main app
        function useThisPlant(plantName) {
            // Store selection for main app
            localStorage.setItem('datasetPlantMatch', JSON.stringify({
                plantName: plantName,
                source: 'dataset_match',
                timestamp: Date.now()
            }));
            
            alert(`✅ Plant Selected: ${plantName}\n\nNow go to the main app and upload your image. It will use this dataset match.`);
            window.open('/', '_blank');
        }
        
        // Show all dataset images
        function showAllDataset() {
            const datasetDiv = document.getElementById('datasetSection');
            
            let html = '<div class="result-section"><h2>📚 Complete Dataset (First 50 plants)</h2>';
            html += '<div class="dataset-grid">';
            
            plantDataset.slice(0, 50).forEach(plant => {
                html += `
                    <div class="dataset-item">
                        <img src="${plant.photo}" alt="${plant.scientificName}" onerror="this.src='https://via.placeholder.com/200x150/f8f9fa/6c757d?text=Image+Not+Found'">
                        <h4 style="font-size: 0.9em; margin: 5px 0;">${plant.scientificName}</h4>
                        <p style="font-size: 0.8em; color: #666; margin: 2px 0;">${plant.localName}</p>
                        <button onclick="useThisPlant('${plant.scientificName}')" class="btn-primary" style="font-size: 0.8em; padding: 5px 10px;">Select</button>
                    </div>
                `;
            });
            
            html += '</div></div>';
            datasetDiv.innerHTML = html;
        }
        
        // Show plants that might be similar
        function showSimilarPlants() {
            const resultsDiv = document.getElementById('resultsSection');
            
            // Show random selection of plants as suggestions
            const randomPlants = plantDataset.sort(() => 0.5 - Math.random()).slice(0, 10);
            
            let html = '<div class="result-section"><h2>🌿 Suggested Plants from Dataset</h2>';
            html += '<p>Since no direct match was found, here are some plants from your dataset:</p>';
            html += '<div class="dataset-grid">';
            
            randomPlants.forEach(plant => {
                html += `
                    <div class="dataset-item">
                        <img src="${plant.photo}" alt="${plant.scientificName}" onerror="this.src='https://via.placeholder.com/200x150/f8f9fa/6c757d?text=Image+Not+Found'">
                        <h4 style="font-size: 0.9em; margin: 5px 0;">${plant.scientificName}</h4>
                        <p style="font-size: 0.8em; color: #666; margin: 2px 0;">${plant.localName}</p>
                        <button onclick="useThisPlant('${plant.scientificName}')" class="btn-primary" style="font-size: 0.8em; padding: 5px 10px;">Select</button>
                    </div>
                `;
            });
            
            html += '</div></div>';
            resultsDiv.innerHTML = html;
        }
        
        // Handle file input
        document.getElementById('imageInput').addEventListener('change', function(e) {
            if (e.target.files[0]) {
                uploadedImage = e.target.files[0];
                const img = document.getElementById('imagePreview');
                img.src = URL.createObjectURL(e.target.files[0]);
                img.style.display = 'block';
                document.getElementById('findBtn').disabled = false;
            }
        });
        
        // Initialize
        loadPlantDataset();
    </script>
</body>
</html>
