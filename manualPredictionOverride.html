<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Plant Identification</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .upload-section { margin: 20px 0; padding: 20px; border: 2px dashed #007bff; border-radius: 8px; text-align: center; }
        .plant-selector { margin: 20px 0; padding: 20px; border: 2px solid #28a745; border-radius: 8px; }
        .result-section { margin: 20px 0; padding: 20px; border: 2px solid #17a2b8; border-radius: 8px; }
        button { padding: 12px 24px; margin: 10px 5px; cursor: pointer; border: none; border-radius: 5px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        select { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px; }
        #imagePreview { max-width: 300px; max-height: 300px; margin: 10px; border: 2px solid #ddd; }
        .plant-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌿 Manual Plant Identification System</h1>
        <div class="warning">
            <h3>⚠️ AI Model Issue Detected</h3>
            <p>Your AI model is stuck predicting "Abelmoschus sagittifolius" for all images. Use this manual system until the model is fixed.</p>
        </div>
        
        <div class="upload-section">
            <h2>📸 Upload Plant Image</h2>
            <input type="file" id="imageInput" accept="image/*">
            <br>
            <img id="imagePreview" style="display: none;">
        </div>
        
        <div class="plant-selector">
            <h2>🔍 Select Correct Plant</h2>
            <p>Since the AI model is not working, please manually select the correct plant from the dropdown:</p>
            
            <label for="plantSelect">Choose the plant in your image:</label>
            <select id="plantSelect">
                <option value="">-- Select a plant --</option>
            </select>
            
            <br><br>
            <button onclick="showPlantInfo()" class="btn-success">Get Plant Information</button>
            <button onclick="sendToMainApp()" class="btn-primary">Use in Main App</button>
        </div>
        
        <div class="result-section" id="resultSection" style="display: none;">
            <h2>🌿 Plant Information</h2>
            <div id="plantInfo"></div>
        </div>
        
        <div class="plant-selector">
            <h2>🔧 Quick Fixes</h2>
            <button onclick="clearModelCache()" class="btn-warning">Clear Model Cache</button>
            <button onclick="resetPredictionHistory()" class="btn-warning">Reset Prediction History</button>
            <button onclick="testRandomPlant()" class="btn-primary">Test Random Plant</button>
        </div>
    </div>

    <script>
        const plantClasses = [
            "Abelmoschus sagittifolius", "Abrus precatorius", "Abutilon indicum", "Acanthus integrifolius",
            "Acorus tatarinowii", "Agave americana", "Ageratum conyzoides", "Allium ramosum",
            "Alocasia macrorrhizos", "Aloe vera", "Alpinia officinarum", "Amomum longiligulare",
            "Ampelopsis cantoniensis", "Andrographis paniculata", "Angelica dahurica", "Ardisia sylvestris",
            "Artemisia vulgaris", "Artocarpus altilis", "Artocarpus heterophyllus", "Artocarpus lakoocha",
            "Asparagus cochinchinensis", "Asparagus officinalis", "Averrhoa carambola", "Baccaurea sp.",
            "Barleria lupulina", "Bengal arum", "Berchemia lineata", "Bidens pilosa",
            "Bischofia trifoliata", "Blackberry lily", "Blumea balsamifera", "Boehmeria nivea",
            "Breynia vitis", "Bryophyllum pinnatum", "Caesalpinia sappan", "Camellia sinensis",
            "Cananga odorata", "Carica papaya", "Cassia alata", "Catharanthus roseus",
            "Centella asiatica", "Chromolaena odorata", "Chrysanthemum morifolium", "Citrus aurantifolia",
            "Citrus hystrix", "Citrus maxima", "Clerodendrum chinense", "Clerodendrum serratum",
            "Glycyrrhiza uralensis Fisch.", "Cocos nucifera", "Coix lacryma-jobi", "Coleus amboinicus",
            "Cordyline fruticosa", "Costus speciosus", "Crescentia cujete L.", "Curcuma longa",
            "Cymbopogon citratus", "Cyperus rotundus", "Datura metel", "Dendrobium nobile",
            "Derris elliptica", "Dimocarpus longan", "Dioscorea persimilis", "Eichhornia crassipes",
            "Eleutherine bulbosa", "Erythrina variegata", "Eupatorium fortunei", "Eupatorium triplinerve",
            "Euphorbia hirta", "Euphorbia pulcherrima", "Euphorbia tirucalli", "Euphorbia tithymaloides",
            "Eurycoma longifolia", "Excoecaria cochinchinensis", "Excoecaria sp", "Fallopia multiflora",
            "Ficus auriculata", "Ficus racemosa", "Fructus lycii", "Glochidion eriocarpum",
            "Glycosmis pentaphylla", "Gonocaryum lobbianum", "Gymnema sylvestre", "Gynura divaricata",
            "Hemerocallis fulva", "Hemigraphis glaucescens", "Hibiscus mutabilis", "Hibiscus rosa-sinensis",
            "Hibiscus sabdariffa", "Holarrhena antidysenterica", "Homalomena occulta", "Houttuynia cordata",
            "Imperata cylindrica", "Iris domestica", "Ixora coccinea", "Jasminum sambac",
            "Jatropha gossypiifolia", "Jatropha multifida", "Jatropha podagrica", "Justicia gendarussa",
            "Kalanchoe pinnata", "Lactuca indica", "Lantana camara", "Lawsonia inermis",
            "Leea rubra", "Litsea glutinosa", "Lonicera dasystyla", "Lpomoea sp",
            "Maesa indica", "Mallotus barbatus", "Mangifera indica", "Melastoma malabathricum",
            "Mentha spicata", "Microcos tomentosa", "Micromelum falcatum", "Millettia pulchra",
            "Mimosa pudica", "Morinda citrifolia", "Moringa oleifera", "Morus alba",
            "Mussaenda philippica", "Nelumbo nucifera", "Ocimum basilicum", "Ocimum gratissimum",
            "Ocimum sanctum", "Oenanthe javanica", "Ophiopogon japonicus", "Paederia lanuginosa",
            "Pandanus amaryllifolius", "Pandanus sp.", "Pandanus tectorius", "Parameria laevigata",
            "Passiflora foetida", "Pereskia sacharosa", "Phyllanthus reticulatus", "Phyllanthus urinaria",
            "Piper betle", "Piper nigrum", "Plantago major", "Plumeria rubra",
            "Polyscias scutellaria", "Premna serratifolia", "Psidium guajava", "Psychotria reevesii Wall.",
            "Punica granatum", "Quisqualis indica", "Rauvolfia serpentina", "Rauvolfia tetraphylla",
            "Rhinacanthus nasutus", "Rhodomyrtus tomentosa", "Ruellia tuberosa", "Sanseviera canaliculata Carr",
            "Holarrhena pubescens", "Sarcandra glabra", "Sauropus androgynus", "Schefflera heptaphylla",
            "Schefflera venulosa", "Senna alata", "Sida acuta Burm.", "Solanum mammosum",
            "Solanum torvum", "Spilanthes acmella", "Spondias dulcis", "Stachytarpheta jamaicensis",
            "Stephania dielsiana", "Stereospermum chelonoides", "Streptocaulon juventas", "Syzygium nervosum",
            "Tabernaemontana divaricata", "Tacca subflabellata", "Tamarindus indica", "Terminalia catappa",
            "Tradescantia discolor", "Trichanthera gigantea", "Vernonia amygdalina", "Vitex negundo",
            "Xanthium strumarium", "Zanthoxylum avicennae", "Zingiber officinale", "Ziziphus mauritiana",
            "Helicteres hirsuta"
        ];

        // Populate plant selector
        function populatePlantSelector() {
            const select = document.getElementById('plantSelect');
            plantClasses.forEach((plant, index) => {
                const option = document.createElement('option');
                option.value = index;
                option.textContent = `${plant} (Index: ${index})`;
                select.appendChild(option);
            });
        }

        // Show plant information
        async function showPlantInfo() {
            const select = document.getElementById('plantSelect');
            const selectedIndex = select.value;
            
            if (!selectedIndex) {
                alert('Please select a plant first');
                return;
            }
            
            const plantName = plantClasses[selectedIndex];
            
            try {
                const response = await fetch(`/api/v1/plant/${encodeURIComponent(plantName)}`);
                const data = await response.json();
                
                if (data.success && data.plant) {
                    const plant = data.plant;
                    document.getElementById('plantInfo').innerHTML = `
                        <div class="plant-info">
                            <h3>${plant.scientificName}</h3>
                            <p><strong>Local Name:</strong> ${plant.localName}</p>
                            <p><strong>Medicinal Features:</strong> ${plant.features}</p>
                            ${plant.photo ? `<img src="${plant.photo}" alt="${plant.scientificName}" style="max-width: 200px; border-radius: 5px;">` : ''}
                        </div>
                    `;
                    document.getElementById('resultSection').style.display = 'block';
                } else {
                    document.getElementById('plantInfo').innerHTML = `
                        <div class="warning">
                            <p>Plant information not found in database for: ${plantName}</p>
                        </div>
                    `;
                    document.getElementById('resultSection').style.display = 'block';
                }
            } catch (error) {
                document.getElementById('plantInfo').innerHTML = `
                    <div class="warning">
                        <p>Error fetching plant information: ${error.message}</p>
                    </div>
                `;
                document.getElementById('resultSection').style.display = 'block';
            }
        }

        // Send to main app
        function sendToMainApp() {
            const select = document.getElementById('plantSelect');
            const selectedIndex = select.value;
            
            if (!selectedIndex) {
                alert('Please select a plant first');
                return;
            }
            
            const plantName = plantClasses[selectedIndex];
            
            // Store the manual selection in localStorage for the main app to use
            localStorage.setItem('manualPlantSelection', JSON.stringify({
                plantName: plantName,
                index: selectedIndex,
                timestamp: Date.now(),
                reason: 'Manual selection due to stuck AI model'
            }));
            
            alert(`Manual selection saved: ${plantName}\n\nNow go to the main app and upload your image. It will use this manual selection instead of the stuck AI prediction.`);
            
            // Open main app
            window.open('/', '_blank');
        }

        // Clear model cache
        function clearModelCache() {
            localStorage.removeItem('modelCache');
            localStorage.removeItem('previousPredictions');
            alert('Model cache cleared. Try refreshing the main app.');
        }

        // Reset prediction history
        function resetPredictionHistory() {
            localStorage.setItem('resetPredictionHistory', 'true');
            alert('Prediction history will be reset on next use of main app.');
        }

        // Test random plant
        function testRandomPlant() {
            const randomIndex = Math.floor(Math.random() * plantClasses.length);
            const select = document.getElementById('plantSelect');
            select.value = randomIndex;
            showPlantInfo();
        }

        // Handle file input
        document.getElementById('imageInput').addEventListener('change', function(e) {
            if (e.target.files[0]) {
                const img = document.getElementById('imagePreview');
                img.src = URL.createObjectURL(e.target.files[0]);
                img.style.display = 'block';
            }
        });

        // Initialize
        populatePlantSelector();
    </script>
</body>
</html>
