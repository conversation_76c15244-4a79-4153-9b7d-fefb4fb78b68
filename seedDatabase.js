import mongoose from 'mongoose';
import fs from 'fs';
import csvtojson from 'csvtojson';
import dotenv from 'dotenv';
import plantModel from './models/plantModel.js';

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/med-plants');

const seedDatabase = async () => {
  try {
    // Clear existing data
    await plantModel.deleteMany({});
    console.log('Existing plant data cleared');

    // Read CSV file
    const csvFilePath = './public/uploads/plant-features.csv';
    const jsonArray = await csvtojson().fromFile(csvFilePath);

    // Insert data into database
    await plantModel.insertMany(jsonArray);
    console.log(`${jsonArray.length} plants added to database`);

    console.log('Database seeding completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

seedDatabase();