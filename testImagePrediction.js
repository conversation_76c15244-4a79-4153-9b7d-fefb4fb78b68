import fs from 'fs';
import path from 'path';
import axios from 'axios';
import FormData from 'form-data';

// Test function to upload an image and get prediction
async function testImagePrediction() {
  try {
    // Path to test image
    const imagePath = path.join(process.cwd(), 'public', 'uploads', 'sample.jpg');
    
    // Check if the image exists
    if (!fs.existsSync(imagePath)) {
      console.error('Test image not found at:', imagePath);
      return;
    }
    
    console.log('Using test image:', imagePath);
    
    // Create form data
    const formData = new FormData();
    formData.append('image', fs.createReadStream(imagePath));
    
    // Add a test prediction
    formData.append('predictedPlant', 'Jatropha podagrica');
    
    // Send request to the server
    const response = await axios.post('http://localhost:8080/api/v1/plant/result', formData, {
      headers: {
        ...formData.getHeaders(),
      },
    });
    
    console.log('Server response:', response.data);
    
    // Test without prediction (server should generate one)
    const formData2 = new FormData();
    formData2.append('image', fs.createReadStream(imagePath));
    
    const response2 = await axios.post('http://localhost:8080/api/v1/plant/result', formData2, {
      headers: {
        ...formData2.getHeaders(),
      },
    });
    
    console.log('Server response (without prediction):', response2.data);
    
    console.log('✅ Test completed successfully');
  } catch (error) {
    console.error('Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
  }
}

// Run the test
testImagePrediction();