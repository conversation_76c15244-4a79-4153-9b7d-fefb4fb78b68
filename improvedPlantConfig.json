{"plantClasses": ["<PERSON><PERSON><PERSON> sagittifolius", "<PERSON><PERSON><PERSON> precatorius", "Abutilon indicum", "Acanthus integrifolius", "<PERSON><PERSON><PERSON>i", "Agave americana", "Ageratum conyzoides", "Allium ramosum", "Alocasia macrorrhizos", "Aloe vera", "Alpinia officinarum", "Amomum longiligulare", "Ampelopsis cantoniensis", "Andrograp<PERSON> paniculata", "<PERSON>", "<PERSON><PERSON>sia sylvestris", "Artemis<PERSON> vulgaris", "Artocar<PERSON> altilis", "Artocar<PERSON> heterophyllus", "Artocar<PERSON> la<PERSON>a", "Aspar<PERSON><PERSON> cochinchinensis", "Aspar<PERSON><PERSON> officinalis", "Averrhoa carambola", "Baccaurea sp.", "<PERSON><PERSON><PERSON> lupulina", "Bengal arum", "Berchemia lineata", "Bidens pilosa", "<PERSON><PERSON><PERSON>ia trifoliata", "Blackberry lily", "Blume<PERSON> balsamifera", "Bo<PERSON><PERSON><PERSON> nivea", "Breynia vitis", "<PERSON><PERSON><PERSON><PERSON><PERSON> sappan", "<PERSON>ery<PERSON> speciosa", "Callisia fragrans", "Calophyllum inophyllum", "Calotropis gigantea", "Camellia chrysantha", "Caprifoliaceae", "Capsicum annuum", "Carica papaya", "Cat<PERSON>nt<PERSON> roseus", "<PERSON><PERSON><PERSON>i", "<PERSON>losia argentea", "Centella asiatica", "Citrus aurantifolia", "Citrus hystrix", "Clausena indica", "Cleistocalyx operculatus", "Clerodendrum inerme", "Clinacan<PERSON> nutans", "Glycyrrhiza uralensis <PERSON>.", "Coix lacryma-jobi", "<PERSON><PERSON><PERSON> fruticosa", "<PERSON><PERSON><PERSON> speciosus", "Crescentia cujete L.", "Crinum asiaticum", "Crinum latifolium", "Crot<PERSON> oblongifolius", "Croton <PERSON>ensis", "Curculigo gracilis", "Curculigo orchioides", "Cymbopogon", "Datura metel", "<PERSON><PERSON> elliptica", "<PERSON>lla ensifolia", "Diclip<PERSON> chinensis", "<PERSON><PERSON><PERSON><PERSON> longan", "Di<PERSON><PERSON><PERSON> persimilis", "<PERSON><PERSON><PERSON>ia crassipes", "Eleuther<PERSON> bulbosa", "<PERSON><PERSON><PERSON><PERSON> variegata", "Eupatorium fortunei", "Eupatorium triplinerve", "Eup<PERSON><PERSON> hirta", "Eup<PERSON><PERSON> pulcherrima", "<PERSON><PERSON><PERSON><PERSON> tirucalli", "<PERSON>up<PERSON><PERSON> tithymaloides", "Eurycoma longifolia", "<PERSON><PERSON><PERSON><PERSON> cochinchinensis", "Excoecaria sp", "Fall<PERSON><PERSON> multiflora", "Ficus auriculata", "Ficus racemosa", "<PERSON><PERSON><PERSON> l<PERSON>i", "Glochidion eriocarpum", "Glycosmis pentaphylla", "Gonocaryum lobbianum", "Gymne<PERSON> sylvestre", "G<PERSON><PERSON> divaricata", "Hemerocall<PERSON> fulva", "Hemi<PERSON><PERSON> glaucescens", "Hi<PERSON><PERSON> mutabilis", "Hibiscus rosa-sinensis", "Hi<PERSON><PERSON> sabdariffa", "Holarrhena antidysenterica", "<PERSON><PERSON><PERSON><PERSON> occulta", "<PERSON><PERSON><PERSON><PERSON> cordata", "Imperata cylindrica", "Iris domestica", "<PERSON><PERSON><PERSON> coccinea", "Jasminum sambac", "Jatropha gossypiifolia", "Jatropha multifida", "Jatropha podagrica", "<PERSON><PERSON><PERSON> gend<PERSON>", "<PERSON><PERSON><PERSON><PERSON> pinnata", "Lactuca indica", "<PERSON><PERSON><PERSON> camara", "<PERSON><PERSON> inermis", "<PERSON>a rubra", "Litsea glutinosa", "Lonicera dasystyla", "Lpomoea sp", "Maesa indica", "Mall<PERSON><PERSON> barbatus", "Mangifera indica", "Melastoma mala<PERSON>icum", "Men<PERSON> spicata", "Microcos tomentosa", "Micromelum falcatum", "Millettia pulchra", "<PERSON><PERSON>a pudica", "<PERSON><PERSON><PERSON> citrifolia", "<PERSON>ringa oleifera", "Morus alba", "Mussaenda philippica", "Nelumbo nucifera", "Ocimum basilicum", "Ocimum gratissimum", "Ocimum sanctum", "Oenanthe javanica", "Ophiopogon japonicus", "<PERSON><PERSON><PERSON> lanuginosa", "<PERSON><PERSON><PERSON> amaryllifolius", "Pandanus sp.", "<PERSON><PERSON><PERSON> tectorius", "Parameria laevigata", "Passiflora foetida", "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "<PERSON><PERSON><PERSON><PERSON> odorata", "Phlogacant<PERSON> turgidus", "Phrynium placentarium", "<PERSON><PERSON>lant<PERSON> reticulatus", "<PERSON> betle", "Piper sarmentosum", "Plantago", "Platycladus orientalis", "Plectrant<PERSON> amboinicus", "Pluchea pteropoda <PERSON>", "Plukenetia volubilis", "Plumbago indica", "Plumeria rubra", "Polyginum cuspidatum", "<PERSON><PERSON><PERSON><PERSON> fruticosa", "<PERSON><PERSON><PERSON><PERSON>i", "Polys<PERSON><PERSON> scutellaria", "Pouzolzia zeylanica", "<PERSON><PERSON>na serratifolia", "Pseuderanthemum latifolium", "Psidium guajava", "Psychotria <PERSON>ii <PERSON>.", "Psychotria rubra", "Quisqualis indica", "Rauvolfia", "<PERSON><PERSON><PERSON><PERSON><PERSON> tetraphylla", "Rhinacant<PERSON> nasutus", "R<PERSON>dom<PERSON><PERSON> tomentosa", "<PERSON><PERSON><PERSON> tuberosa", "<PERSON><PERSON><PERSON><PERSON> canaliculata Carr", "<PERSON><PERSON><PERSON>hen<PERSON> pubescens", "Sarcandra glabra", "<PERSON><PERSON><PERSON> androgynus", "<PERSON><PERSON><PERSON><PERSON> heptaphylla", "<PERSON><PERSON><PERSON><PERSON> venulosa", "<PERSON><PERSON> alata", "<PERSON>a acuta <PERSON>.", "Solanum mammosum", "Solanum torvum", "<PERSON><PERSON><PERSON><PERSON> acmella", "Spondias dulcis", "Stachytarphe<PERSON> jamaicensis", "<PERSON><PERSON>iana", "Stereospermum chelonoides", "Streptocaulon juventas", "Syzygium nervosum", "Tabernaemontana divaricata", "Tacca subflabellata", "<PERSON><PERSON><PERSON><PERSON> indica", "Terminalia catappa", "Tradescantia discolor", "Trichanthera gigantea", "<PERSON><PERSON> amygdalina", "Vitex negundo", "Xanthium strumarium", "Zanthoxylum avicennae", "Zingiber officinale", "<PERSON><PERSON><PERSON><PERSON> mauritiana", "<PERSON><PERSON><PERSON><PERSON> hirsuta"], "modelConfig": {"inputShape": [128, 128, 3], "outputClasses": 200, "preprocessing": {"resize": [128, 128], "normalize": true, "normalizeRange": [0, 1]}}, "trainingRecommendations": {"imageQuality": ["Use high-resolution images (at least 512x512)", "Ensure good lighting and clear focus", "Include multiple angles of each plant", "Show distinctive features (leaves, flowers, stems)", "Remove background clutter"], "datasetSize": ["Minimum 100 images per plant class", "Recommended 500+ images per class for better accuracy", "Include seasonal variations", "Include different growth stages", "Include healthy and diseased specimens"], "preprocessing": ["Resize to 128x128 pixels", "Normalize pixel values to [0,1] range", "Apply data augmentation (rotation, flip, zoom)", "Maintain aspect ratio during resize", "Use consistent preprocessing for training and inference"]}, "commonAlternativeNames": {"Hibiscus rosa-sinensis": ["Hibiscus rosa sinensis", "Chinese hibiscus", "Shoe flower"], "Aloe vera": ["Aloe barbadensis", "True aloe"], "Moringa oleifera": ["Drumstick tree", "Miracle tree"], "Centella asiatica": ["<PERSON><PERSON> kola", "Indian pennywort"], "Curcuma longa": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "Ocimum sanctum": ["Holy basil", "<PERSON><PERSON><PERSON>"], "Azadirachta indica": ["<PERSON><PERSON><PERSON>", "Indian lilac"], "Withania somnifera": ["Ashwagandha", "Winter cherry"], "Bacopa monnieri": ["<PERSON><PERSON><PERSON>", "Water hyssop"], "Gymnema sylvestre": ["<PERSON><PERSON><PERSON>", "Sugar destroyer"]}}