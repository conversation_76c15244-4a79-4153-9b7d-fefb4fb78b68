import React, { useState } from "react";
import { useDropzone } from "react-dropzone";
import upload from "../Assets/upload_image.png";
import "../style/dragDropStyle.css";
import Footer from "./footer";
import { useNavigate } from "react-router-dom";
import * as tf from "@tensorflow/tfjs";
import { plantClasses } from "../data/plantClasses";
import axios from "axios";

// Function to load an image and convert it to a tensor
async function loadImage(file) {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      const img = new Image();
      img.onload = () => resolve(tf.browser.fromPixels(img));
      img.src = event.target.result;
    };
    reader.readAsDataURL(file);
  });
}

function preprocessImage(image) {
  const resizedImage = image.resizeBilinear([128, 128]); // Adjust size if necessary
  return resizedImage;
}

const Dragdrop = () => {
  const [files, setFiles] = useState([]);
  const [modelLoading, setModelLoading] = useState(true);
  const [model, setModel] = useState(null);
  const navigate = useNavigate();

  // Load the TensorFlow model
  const loadModel = async () => {
    try {
      const model = await tf.loadGraphModel("/model/model.json");
      setModel(model);
      setModelLoading(false);
    } catch (error) {
      console.error("Error loading model:", error);
      setModelLoading(false);
    }
  };

  React.useEffect(() => {
    loadModel();
  }, []);

  const getPlantName = async (files) => {
    try {
      setModelLoading(true); // Show loading state
      
      // First try client-side prediction
      try {
        const image = await loadImage(files[0]);
        let tensor = preprocessImage(image);
        tensor = tensor.expandDims(0);
        const predictions = await model.predict(tensor).data();
        
        // Log all prediction values for debugging
        console.log("Raw predictions:", Array.from(predictions));
        
        // Find the index with the highest prediction value
        let index = Array.from(predictions).indexOf(Math.max(...Array.from(predictions)));
        console.log("Predicted index:", index);
        
        // Ensure index is within bounds of plantClasses array
        if (index >= 0 && index < plantClasses.length) {
          const predictedPlant = plantClasses[index];
          console.log("Client-side prediction:", predictedPlant);
          
          // Also upload to server as backup
          uploadToServer(files[0], predictedPlant);
          
          // Navigate to result page
          navigate(`/result/${predictedPlant}`);
        } else {
          console.error("Prediction index out of bounds:", index);
          // Fall back to server-side prediction
          uploadToServer(files[0]);
        }
      } catch (clientError) {
        console.error("Client-side prediction failed:", clientError);
        // Fall back to server-side prediction
        uploadToServer(files[0]);
      }
    } catch (error) {
      console.error("Error predicting plant:", error);
      setModelLoading(false);
    }
  };
  
  const uploadToServer = async (file, clientPrediction = null) => {
    try {
      const formData = new FormData();
      formData.append("image", file);
      
      // If we have a client prediction, include it in the request
      if (clientPrediction) {
        formData.append("predictedPlant", clientPrediction);
      }
      
      const response = await axios.post("/api/v1/plant/result", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      
      console.log("Server response:", response.data);
      
      // If we don't have a client prediction, use the server's
      if (!clientPrediction && response.data.success) {
        navigate(`/result/${response.data.name}`);
      }
      
      setModelLoading(false);
    } catch (error) {
      console.error("Error uploading to server:", error);
      setModelLoading(false);
      
      // If we have a client prediction, still use it despite server error
      if (clientPrediction) {
        navigate(`/result/${clientPrediction}`);
      }
    }
  };

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      "image/*": [],
    },
    onDrop: (acceptedFiles) => {
      getPlantName(acceptedFiles);
    },
  });

  return (
    <>
      {!modelLoading && (
        <>
          <section className="container">
            <div {...getRootProps({ className: "dropzone" })}>
              <input {...getInputProps()} />
              <img src={upload} alt="upload" style={{ width: 60 }} />
              <h2>
                Click here to add files <br />
                or
                <br /> Drag & Drop files here
              </h2>
              <button className="btn btn-outline-info" style={{ marginTop: 0 }}>
                Browse Files
              </button>
            </div>
          </section>
          <Footer />
        </>
      )}
    </>
  );
};

export default Dragdrop;