<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Model Stuck Diagnosis</title>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.11.0/dist/tf.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .test-section { margin: 20px 0; padding: 20px; border: 2px solid #ddd; border-radius: 8px; }
        .result { margin: 10px 0; padding: 15px; border-radius: 5px; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 12px 24px; margin: 10px 5px; cursor: pointer; border: none; border-radius: 5px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        #imagePreview { max-width: 200px; max-height: 200px; margin: 10px; border: 2px solid #ddd; }
        .prediction-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
        .prediction-card { padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Model "Stuck" Diagnosis Tool</h1>
        <p>This tool helps identify why your model gives the same result for different images</p>
        
        <div class="test-section">
            <h2>🧪 Quick Diagnosis Tests</h2>
            <button onclick="testRandomInputs()" class="btn-primary">Test with Random Inputs</button>
            <button onclick="testDifferentImages()" class="btn-primary">Test Multiple Images</button>
            <button onclick="analyzeModelWeights()" class="btn-primary">Analyze Model</button>
            <div id="quickResults"></div>
        </div>
        
        <div class="test-section">
            <h2>🖼️ Image Testing</h2>
            <input type="file" id="imageInput" accept="image/*" multiple>
            <br>
            <div id="imagePreviewContainer"></div>
            <br>
            <button onclick="testAllImages()" id="testBtn" class="btn-primary" disabled>Test All Images</button>
            <div id="imageResults"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 Prediction Analysis</h2>
            <div id="predictionAnalysis"></div>
        </div>
        
        <div class="test-section">
            <h2>🔧 Recommended Fixes</h2>
            <div id="recommendedFixes"></div>
        </div>
    </div>

    <script>
        const plantClasses = [
            "Abelmoschus sagittifolius", "Abrus precatorius", "Abutilon indicum", "Acanthus integrifolius",
            "Acorus tatarinowii", "Agave americana", "Ageratum conyzoides", "Allium ramosum",
            "Alocasia macrorrhizos", "Aloe vera", "Alpinia officinarum", "Amomum longiligulare",
            "Ampelopsis cantoniensis", "Andrographis paniculata", "Angelica dahurica", "Ardisia sylvestris",
            "Artemisia vulgaris", "Artocarpus altilis", "Artocarpus heterophyllus", "Artocarpus lakoocha",
            "Asparagus cochinchinensis", "Asparagus officinalis", "Averrhoa carambola", "Baccaurea sp.",
            "Barleria lupulina", "Bengal arum", "Berchemia lineata", "Bidens pilosa",
            "Bischofia trifoliata", "Blackberry lily", "Blumea balsamifera", "Boehmeria nivea",
            "Breynia vitis", "Bryophyllum pinnatum", "Caesalpinia sappan", "Camellia sinensis",
            "Cananga odorata", "Carica papaya", "Cassia alata", "Catharanthus roseus",
            "Centella asiatica", "Chromolaena odorata", "Chrysanthemum morifolium", "Citrus aurantifolia",
            "Citrus hystrix", "Citrus maxima", "Clerodendrum chinense", "Clerodendrum serratum",
            "Glycyrrhiza uralensis Fisch.", "Cocos nucifera", "Coix lacryma-jobi", "Coleus amboinicus",
            "Cordyline fruticosa", "Costus speciosus", "Crescentia cujete L.", "Curcuma longa",
            "Cymbopogon citratus", "Cyperus rotundus", "Datura metel", "Dendrobium nobile",
            "Derris elliptica", "Dimocarpus longan", "Dioscorea persimilis", "Eichhornia crassipes",
            "Eleutherine bulbosa", "Erythrina variegata", "Eupatorium fortunei", "Eupatorium triplinerve",
            "Euphorbia hirta", "Euphorbia pulcherrima", "Euphorbia tirucalli", "Euphorbia tithymaloides",
            "Eurycoma longifolia", "Excoecaria cochinchinensis", "Excoecaria sp", "Fallopia multiflora",
            "Ficus auriculata", "Ficus racemosa", "Fructus lycii", "Glochidion eriocarpum",
            "Glycosmis pentaphylla", "Gonocaryum lobbianum", "Gymnema sylvestre", "Gynura divaricata",
            "Hemerocallis fulva", "Hemigraphis glaucescens", "Hibiscus mutabilis", "Hibiscus rosa-sinensis",
            "Hibiscus sabdariffa", "Holarrhena antidysenterica", "Homalomena occulta", "Houttuynia cordata",
            "Imperata cylindrica", "Iris domestica", "Ixora coccinea", "Jasminum sambac",
            "Jatropha gossypiifolia", "Jatropha multifida", "Jatropha podagrica", "Justicia gendarussa",
            "Kalanchoe pinnata", "Lactuca indica", "Lantana camara", "Lawsonia inermis",
            "Leea rubra", "Litsea glutinosa", "Lonicera dasystyla", "Lpomoea sp",
            "Maesa indica", "Mallotus barbatus", "Mangifera indica", "Melastoma malabathricum",
            "Mentha spicata", "Microcos tomentosa", "Micromelum falcatum", "Millettia pulchra",
            "Mimosa pudica", "Morinda citrifolia", "Moringa oleifera", "Morus alba",
            "Mussaenda philippica", "Nelumbo nucifera", "Ocimum basilicum", "Ocimum gratissimum",
            "Ocimum sanctum", "Oenanthe javanica", "Ophiopogon japonicus", "Paederia lanuginosa",
            "Pandanus amaryllifolius", "Pandanus sp.", "Pandanus tectorius", "Parameria laevigata",
            "Passiflora foetida", "Pereskia sacharosa", "Phyllanthus reticulatus", "Phyllanthus urinaria",
            "Piper betle", "Piper nigrum", "Plantago major", "Plumeria rubra",
            "Polyscias scutellaria", "Premna serratifolia", "Psidium guajava", "Psychotria reevesii Wall.",
            "Punica granatum", "Quisqualis indica", "Rauvolfia serpentina", "Rauvolfia tetraphylla",
            "Rhinacanthus nasutus", "Rhodomyrtus tomentosa", "Ruellia tuberosa", "Sanseviera canaliculata Carr",
            "Holarrhena pubescens", "Sarcandra glabra", "Sauropus androgynus", "Schefflera heptaphylla",
            "Schefflera venulosa", "Senna alata", "Sida acuta Burm.", "Solanum mammosum",
            "Solanum torvum", "Spilanthes acmella", "Spondias dulcis", "Stachytarpheta jamaicensis",
            "Stephania dielsiana", "Stereospermum chelonoides", "Streptocaulon juventas", "Syzygium nervosum",
            "Tabernaemontana divaricata", "Tacca subflabellata", "Tamarindus indica", "Terminalia catappa",
            "Tradescantia discolor", "Trichanthera gigantea", "Vernonia amygdalina", "Vitex negundo",
            "Xanthium strumarium", "Zanthoxylum avicennae", "Zingiber officinale", "Ziziphus mauritiana",
            "Helicteres hirsuta"
        ];

        let model = null;
        let testResults = [];
        
        // Load model
        async function loadModel() {
            try {
                console.log('Loading model...');
                model = await tf.loadGraphModel('/model/model.json');
                console.log('Model loaded successfully');
                document.getElementById('testBtn').disabled = false;
                
                // Immediately run quick diagnosis
                setTimeout(testRandomInputs, 1000);
            } catch (error) {
                console.error('Error loading model:', error);
                document.getElementById('quickResults').innerHTML = `
                    <div class="error">❌ Failed to load model: ${error.message}</div>
                `;
            }
        }
        
        // Test with random inputs to see if model is stuck
        async function testRandomInputs() {
            if (!model) {
                alert('Model not loaded yet');
                return;
            }
            
            const resultsDiv = document.getElementById('quickResults');
            resultsDiv.innerHTML = '<div class="info">🔄 Testing with random inputs...</div>';
            
            const results = [];
            
            try {
                // Test 5 different random inputs
                for (let i = 0; i < 5; i++) {
                    const randomInput = tf.randomNormal([1, 128, 128, 3]);
                    const predictions = await model.predict(randomInput).data();
                    
                    const predArray = Array.from(predictions);
                    const maxPred = Math.max(...predArray);
                    const maxIndex = predArray.indexOf(maxPred);
                    
                    results.push({
                        test: i + 1,
                        predictedIndex: maxIndex,
                        predictedPlant: plantClasses[maxIndex],
                        confidence: maxPred,
                        allPredictions: predArray
                    });
                    
                    randomInput.dispose();
                }
                
                // Analyze results
                const uniquePredictions = new Set(results.map(r => r.predictedIndex));
                const isStuck = uniquePredictions.size === 1;
                
                let html = `<h3>🧪 Random Input Test Results</h3>`;
                
                if (isStuck) {
                    html += `<div class="error">
                        <h4>❌ MODEL IS STUCK!</h4>
                        <p>All ${results.length} random inputs predicted the same plant: <strong>${results[0].predictedPlant}</strong> (Index: ${results[0].predictedIndex})</p>
                        <p>This indicates a serious model training issue.</p>
                    </div>`;
                } else {
                    html += `<div class="success">
                        <h4>✅ Model is responding to different inputs</h4>
                        <p>Found ${uniquePredictions.size} different predictions from ${results.length} tests.</p>
                    </div>`;
                }
                
                html += `<div class="prediction-grid">`;
                results.forEach(result => {
                    html += `
                        <div class="prediction-card">
                            <strong>Test ${result.test}</strong><br>
                            Plant: ${result.predictedPlant}<br>
                            Index: ${result.predictedIndex}<br>
                            Confidence: ${(result.confidence * 100).toFixed(2)}%
                        </div>
                    `;
                });
                html += `</div>`;
                
                resultsDiv.innerHTML = html;
                
                // Generate recommendations
                generateRecommendations(isStuck, results);
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error testing model: ${error.message}</div>`;
            }
        }
        
        // Test with actual images
        async function testAllImages() {
            const fileInput = document.getElementById('imageInput');
            if (!fileInput.files.length || !model) return;
            
            const resultsDiv = document.getElementById('imageResults');
            resultsDiv.innerHTML = '<div class="info">🔄 Testing images...</div>';
            
            const results = [];
            
            try {
                for (let i = 0; i < fileInput.files.length; i++) {
                    const file = fileInput.files[i];
                    const result = await testSingleImage(file);
                    results.push({ file: file.name, ...result });
                }
                
                // Analyze image results
                const uniquePredictions = new Set(results.map(r => r.predictedIndex));
                const isStuck = uniquePredictions.size === 1;
                
                let html = `<h3>🖼️ Image Test Results</h3>`;
                
                if (isStuck && results.length > 1) {
                    html += `<div class="error">
                        <h4>❌ MODEL IS STUCK ON IMAGES TOO!</h4>
                        <p>All ${results.length} different images predicted: <strong>${results[0].predictedPlant}</strong></p>
                    </div>`;
                } else {
                    html += `<div class="info">
                        <p>Found ${uniquePredictions.size} different predictions from ${results.length} images.</p>
                    </div>`;
                }
                
                html += `<div class="prediction-grid">`;
                results.forEach(result => {
                    html += `
                        <div class="prediction-card">
                            <strong>${result.file}</strong><br>
                            Plant: ${result.predictedPlant}<br>
                            Index: ${result.predictedIndex}<br>
                            Confidence: ${(result.confidence * 100).toFixed(2)}%
                        </div>
                    `;
                });
                html += `</div>`;
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error testing images: ${error.message}</div>`;
            }
        }
        
        async function testSingleImage(file) {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = async () => {
                    const tensor = tf.browser.fromPixels(img);
                    const resized = tensor.resizeBilinear([128, 128]);
                    const normalized = resized.div(255.0);
                    const batched = normalized.expandDims(0);
                    
                    const predictions = await model.predict(batched).data();
                    
                    const predArray = Array.from(predictions);
                    const maxPred = Math.max(...predArray);
                    const maxIndex = predArray.indexOf(maxPred);
                    
                    tensor.dispose();
                    resized.dispose();
                    normalized.dispose();
                    batched.dispose();
                    
                    resolve({
                        predictedIndex: maxIndex,
                        predictedPlant: plantClasses[maxIndex],
                        confidence: maxPred
                    });
                };
                img.src = URL.createObjectURL(file);
            });
        }
        
        function generateRecommendations(isStuck, results) {
            const fixesDiv = document.getElementById('recommendedFixes');
            
            if (isStuck) {
                fixesDiv.innerHTML = `
                    <div class="error">
                        <h3>🚨 CRITICAL ISSUE: Model is Stuck</h3>
                        <p>Your model always predicts: <strong>${results[0].predictedPlant}</strong> (Index: ${results[0].predictedIndex})</p>
                    </div>
                    
                    <h4>🔧 Immediate Fixes:</h4>
                    <div class="warning">
                        <h5>1. Check Model File</h5>
                        <p>• Model may be corrupted or incorrectly saved</p>
                        <p>• Try re-downloading or re-converting the model</p>
                        <p>• Verify model.json and .bin files are complete</p>
                    </div>
                    
                    <div class="warning">
                        <h5>2. Training Issues</h5>
                        <p>• Model was likely trained incorrectly</p>
                        <p>• All training data may have been labeled as the same class</p>
                        <p>• Model architecture may be too simple</p>
                        <p>• Training may have failed to converge properly</p>
                    </div>
                    
                    <div class="warning">
                        <h5>3. Preprocessing Issues</h5>
                        <p>• Input preprocessing may not match training preprocessing</p>
                        <p>• Model may expect different input format</p>
                        <p>• Normalization or scaling may be incorrect</p>
                    </div>
                    
                    <h4>🛠️ Solutions:</h4>
                    <div class="info">
                        <h5>Short-term (Temporary Fix):</h5>
                        <p>• Use a rule-based system based on image features</p>
                        <p>• Implement manual plant identification</p>
                        <p>• Create a lookup system based on image metadata</p>
                    </div>
                    
                    <div class="success">
                        <h5>Long-term (Proper Fix):</h5>
                        <p>• Retrain the model with proper data</p>
                        <p>• Use a pre-trained model and fine-tune it</p>
                        <p>• Implement proper data validation during training</p>
                        <p>• Use a more robust model architecture</p>
                    </div>
                `;
            } else {
                fixesDiv.innerHTML = `
                    <div class="success">
                        <h3>✅ Model is Working</h3>
                        <p>The model responds to different inputs with different predictions.</p>
                        <p>If you're still getting the same results, the issue might be:</p>
                        <ul>
                            <li>Testing with very similar images</li>
                            <li>Images all belonging to the same plant species</li>
                            <li>Prediction correction system overriding results</li>
                        </ul>
                    </div>
                `;
            }
        }
        
        // Handle file input
        document.getElementById('imageInput').addEventListener('change', function(e) {
            const container = document.getElementById('imagePreviewContainer');
            container.innerHTML = '';
            
            for (let i = 0; i < e.target.files.length; i++) {
                const img = document.createElement('img');
                img.src = URL.createObjectURL(e.target.files[i]);
                img.style.maxWidth = '150px';
                img.style.maxHeight = '150px';
                img.style.margin = '5px';
                img.style.border = '1px solid #ddd';
                container.appendChild(img);
            }
        });
        
        // Load model on page load
        loadModel();
    </script>
</body>
</html>
