import { plantClasses } from "../frontend/src/data/plantClasses.js";
import feedbackModel from "../models/feedbackModel.js";
import plantModel from "../models/plantModel.js";

// We'll skip loading the model in the backend
// The model will be used in the frontend only
console.log('Backend will use frontend for model predictions');

// Use the imported plant classes for model prediction
const classLabels = plantClasses;

export const getPlantController = async (req, res) => {
  try {
    console.log(`Fetching plant: ${req.params.name}`);
    const { name } = req.params;
    const decodedName = decodeURIComponent(name);
    
    // Try to find the plant by exact scientific name first
    let plant = await plantModel.findOne({ scientificName: decodedName });
    
    // If not found, try case-insensitive search
    if (!plant) {
      plant = await plantModel.findOne({ 
        scientificName: { $regex: new RegExp('^' + decodedName + '$', 'i') } 
      });
    }
    
    console.log("Plant found in DB:", plant);

    if (!plant) {
      return res.status(404).json({
        success: false,
        message: "Plant not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Plant retrieved successfully",
      plant,
    });
  } catch (error) {
    console.error("Error retrieving plant:", error);
    res.status(500).json({
      success: false,
      error,
      message: "Error in getting plant details",
    });
  }
};

export const uploadPlantController = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: "No file uploaded",
      });
    }

    const filePath = req.file.path;
    console.log("Processing file:", filePath);

    const response = await csv().fromFile(filePath);
    const plantData = response.map((entry) => ({
      scientificName: entry.scientificName?.trim(),
      localName: entry.localName?.trim(),
      features: entry.features?.trim(),
      photo: entry.photo?.trim(),
    }));

    if (plantData.some(p => !p.scientificName || !p.localName || !p.features)) {
      return res.status(400).json({
        success: false,
        message: "Invalid CSV data. Missing required fields.",
      });
    }

    await plantModel.insertMany(plantData);
    console.log("Plants added:", plantData.length);

    res.status(200).json({
      success: true,
      message: "Upload successful",
    });
  } catch (error) {
    console.error("Error uploading plant data:", error);
    res.status(500).json({
      success: false,
      error,
      message: "Error in uploading plant details",
    });
  }
};

export const postFeedback = async (req, res) => {
  try {
    const { score, description } = req.body;
    if (!score || !description) {
      return res.status(400).json({
        success: false,
        message: "Score and description are required!",
      });
    }

    const feed = await new feedbackModel({ score, description }).save();
    res.status(200).json({
      success: true,
      message: "Review posted successfully",
      feed,
    });
  } catch (error) {
    console.error("Error posting feedback:", error);
    res.status(500).json({
      success: false,
      error,
      message: "Error in posting review",
    });
  }
};

export const getFeedback = async (req, res) => {
  try {
    const feed = await feedbackModel.find({}).sort({ createdAt: -1 });
    res.status(200).json({
      success: true,
      totalCount: feed.length,
      message: "Feedback retrieved",
      feed,
    });
  } catch (error) {
    console.error("Error fetching feedback:", error);
    res.status(500).json({
      success: false,
      error,
      message: "Error in getting feedback",
    });
  }
};

export const getResultName = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, message: "No file uploaded" });
    }

    const filePath = req.file.path;
    console.log("Processing file:", filePath);

    // Check if a prediction was provided by the frontend
    const { predictedPlant } = req.body;
    
    if (predictedPlant && classLabels.includes(predictedPlant)) {
      console.log("Using frontend prediction:", predictedPlant);

      // Use the frontend's prediction if it's valid
      res.status(200).json({
        success: true,
        message: "Using frontend prediction",
        name: predictedPlant,
        imagePath: filePath.replace(/\\/g, '/').replace('public/', '') // Make path relative for frontend
      });
    } else {
      console.log("No valid frontend prediction provided, backend cannot perform ML prediction");
      console.log("Available class labels count:", classLabels.length);

      // Since we don't have a backend ML model, we need a frontend prediction
      // Return an error asking the user to try again
      res.status(400).json({
        success: false,
        message: "Unable to predict plant. Please ensure the image is clear and try again. The system requires client-side processing for accurate predictions.",
        error: "No valid prediction available",
        imagePath: filePath.replace(/\\/g, '/').replace('public/', '') // Make path relative for frontend
      });
    }


  } catch (error) {
    console.error("Error processing image:", error);
    res.status(500).json({ success: false, error, message: "Error in processing the image" });
  }
};
