import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Footer from './footer';

const InternetResult = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const plantData = location.state?.plantData;

  if (!plantData) {
    return (
      <div style={{ padding: '50px', textAlign: 'center' }}>
        <h2>No plant data available</h2>
        <button onClick={() => navigate('/')} style={{
          padding: '12px 24px',
          background: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer'
        }}>
          Back to Search
        </button>
      </div>
    );
  }

  const features = plantData.features ? plantData.features.split(',').map(f => f.trim()).filter(f => f.length > 0) : [];

  return (
    <>
      <div className="response-block">
        <div style={{
          background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
          color: 'white',
          padding: '20px',
          borderRadius: '10px 10px 0 0',
          textAlign: 'center'
        }}>
          <h1 style={{
            margin: '0',
            fontSize: '2.5em',
            textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
          }}>
            🌐 {plantData.localName || plantData.name}
          </h1>
          <p style={{
            margin: '10px 0 0 0',
            fontSize: '1.2em',
            opacity: '0.9'
          }}>
            Internet Search Result - External Plant Database
          </p>
        </div>
        
        <div className="response-content" style={{padding: '30px'}}>
          <div className="right_side" style={{flex: '1', marginRight: '30px'}}>
            
            {/* Source Information */}
            <div style={{
              background: '#e3f2fd',
              padding: '20px',
              borderRadius: '10px',
              marginBottom: '20px',
              border: '2px solid #90caf9'
            }}>
              <h3 style={{color: '#1565c0', marginBottom: '15px', fontSize: '1.4em'}}>
                🌐 Source Information
              </h3>
              <div style={{display: 'grid', gap: '10px'}}>
                <div style={{display: 'flex', justifyContent: 'space-between', padding: '8px 0', borderBottom: '1px solid #bbdefb'}}>
                  <strong style={{color: '#1976d2'}}>Source:</strong>
                  <span style={{color: '#1565c0', fontWeight: 'bold'}}>{plantData.source}</span>
                </div>
                <div style={{display: 'flex', justifyContent: 'space-between', padding: '8px 0', borderBottom: '1px solid #bbdefb'}}>
                  <strong style={{color: '#1976d2'}}>Confidence:</strong>
                  <span style={{color: '#1565c0', fontWeight: 'bold'}}>{plantData.confidence}%</span>
                </div>
                <div style={{display: 'flex', justifyContent: 'space-between', padding: '8px 0'}}>
                  <strong style={{color: '#1976d2'}}>Search Type:</strong>
                  <span style={{color: '#1565c0', fontWeight: 'bold'}}>Internet Database</span>
                </div>
              </div>
            </div>

            {/* Scientific Information */}
            <div style={{
              background: '#f8f9fa',
              padding: '20px',
              borderRadius: '10px',
              marginBottom: '20px',
              border: '2px solid #e9ecef'
            }}>
              <h3 style={{color: '#2c5530', marginBottom: '15px', fontSize: '1.4em'}}>
                🔬 Plant Classification
              </h3>
              <div style={{display: 'grid', gap: '10px'}}>
                <div style={{display: 'flex', justifyContent: 'space-between', padding: '8px 0', borderBottom: '1px solid #dee2e6'}}>
                  <strong style={{color: '#495057'}}>Scientific Name:</strong>
                  <span style={{color: '#2c5530', fontWeight: 'bold'}}>{plantData.scientificName || plantData.name}</span>
                </div>
                <div style={{display: 'flex', justifyContent: 'space-between', padding: '8px 0'}}>
                  <strong style={{color: '#495057'}}>Common Name:</strong>
                  <span style={{color: '#2c5530', fontWeight: 'bold'}}>{plantData.localName || 'Not specified'}</span>
                </div>
              </div>
            </div>

            {/* Plant Information */}
            <div style={{
              background: '#fff3cd',
              padding: '20px',
              borderRadius: '10px',
              border: '2px solid #ffeaa7'
            }}>
              <h3 style={{color: '#856404', marginBottom: '15px', fontSize: '1.4em'}}>
                📋 Plant Information
              </h3>
              <ul style={{
                listStyle: 'none',
                padding: '0',
                margin: '0'
              }}>
                {features.length > 0 ? features.map((feature, index) => (
                  <li key={index} style={{
                    padding: '8px 0',
                    borderBottom: '1px solid #ffeaa7',
                    fontSize: '16px',
                    color: '#856404'
                  }}>
                    🌿 {feature}
                  </li>
                )) : (
                  <li style={{
                    padding: '8px 0',
                    fontSize: '16px',
                    color: '#856404',
                    fontStyle: 'italic'
                  }}>
                    ℹ️ Limited information available from internet source
                  </li>
                )}
              </ul>
              
              <div style={{
                marginTop: '20px',
                padding: '15px',
                background: '#fff',
                borderRadius: '8px',
                border: '1px solid #ffeaa7'
              }}>
                <p style={{
                  margin: '0',
                  fontSize: '0.9em',
                  color: '#856404'
                }}>
                  <strong>⚠️ Internet Search Result:</strong> This information comes from external databases. 
                  Accuracy may vary. For medical use, please verify with authoritative botanical sources.
                </p>
              </div>
            </div>
          </div>
          
          <div className="left_side" style={{flex: '0 0 400px'}}>
            <div className="img" style={{
              border: '3px solid #007bff',
              borderRadius: '15px',
              overflow: 'hidden',
              boxShadow: '0 8px 25px rgba(0, 123, 255, 0.3)'
            }}>
              <img 
                src={plantData.photo} 
                alt={plantData.scientificName || plantData.name}
                style={{
                  width: '100%',
                  height: '400px',
                  objectFit: 'cover'
                }}
                onError={(e) => {
                  e.target.src = "https://via.placeholder.com/400x400/e3f2fd/1565c0?text=Internet+Search+Result";
                }}
              />
            </div>
            
            {/* Search Actions */}
            <div style={{
              marginTop: '20px',
              padding: '15px',
              background: '#e8f5e8',
              borderRadius: '10px',
              border: '1px solid #c3e6cb'
            }}>
              <h4 style={{color: '#155724', margin: '0 0 10px 0'}}>
                🔍 Search Options
              </h4>
              <div style={{display: 'flex', flexDirection: 'column', gap: '10px'}}>
                <button
                  onClick={() => navigate('/')}
                  style={{
                    padding: '10px 15px',
                    background: '#28a745',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  🔄 Search Again
                </button>
                <button
                  onClick={() => navigate('/dual-search')}
                  style={{
                    padding: '10px 15px',
                    background: '#007bff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  📚 Browse Database
                </button>
              </div>
            </div>

            {/* Reliability Info */}
            <div style={{
              marginTop: '20px',
              padding: '15px',
              background: '#f8d7da',
              borderRadius: '10px',
              border: '1px solid #f5c6cb'
            }}>
              <h4 style={{color: '#721c24', margin: '0 0 10px 0'}}>
                ⚠️ Reliability Notice
              </h4>
              <ul style={{
                margin: '0',
                paddingLeft: '20px',
                color: '#721c24',
                fontSize: '0.9em'
              }}>
                <li>Internet search results may be less accurate</li>
                <li>Always verify with multiple sources</li>
                <li>Consult experts for medical applications</li>
                <li>Database plants are more reliable</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <div className="d-flex justify-content-center">
        <button
          type="button"
          className="btn btn-primary btn-lg mt-3 mb-5"
          onClick={() => navigate('/feedback')}
        >
          Feedback on Internet Result
        </button>
      </div>

      <Footer />
    </>
  );
};

export default InternetResult;
